"""
提示词模板加载器

基于Jinja2的提示词模板管理系统，支持动态变量注入和模板渲染。
参考deer-flow项目的设计思想，将提示词作为独立的模板文件进行管理。
"""

import os
import dataclasses
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, List, Optional
from jinja2 import Environment, FileSystemLoader, select_autoescape, Template

# 初始化Jinja2环境
PROMPT_ROOT = Path(__file__).parent
env = Environment(
    loader=FileSystemLoader(PROMPT_ROOT),
    autoescape=select_autoescape(),
    trim_blocks=True,
    lstrip_blocks=True,
)


def get_prompt(template_name: str, **kwargs) -> str:
    """
    加载并渲染提示词模板
    
    Args:
        template_name: 模板文件名（不含.md扩展名）
        **kwargs: 模板变量
        
    Returns:
        渲染后的提示词字符串
        
    Raises:
        ValueError: 模板加载或渲染失败
    """
    try:
        # 添加默认变量
        template_vars = {
            "CURRENT_TIME": datetime.now().strftime("%a %b %d %Y %H:%M:%S %z"),
            **kwargs
        }
        
        template = env.get_template(f"{template_name}.md")
        return template.render(**template_vars)
    except Exception as e:
        raise ValueError(f"Error loading/rendering template {template_name}: {e}")


def apply_prompt_template(
    template_name: str, 
    state: Dict[str, Any], 
    configurable: Optional[Dict[str, Any]] = None
) -> List[Dict[str, str]]:
    """
    应用模板变量到提示词模板并返回格式化的消息列表
    
    Args:
        template_name: 要使用的提示词模板名称
        state: 包含要替换变量的状态字典
        configurable: 可配置的额外变量
        
    Returns:
        包含系统提示词的消息列表
        
    Raises:
        ValueError: 模板应用失败
    """
    # 转换状态为模板变量字典
    state_vars = {
        "CURRENT_TIME": datetime.now().strftime("%a %b %d %Y %H:%M:%S %z"),
        **state,
    }
    
    # 添加可配置变量
    if configurable:
        if dataclasses.is_dataclass(configurable):
            state_vars.update(dataclasses.asdict(configurable))
        else:
            state_vars.update(configurable)
    
    try:
        template = env.get_template(f"{template_name}.md")
        system_prompt = template.render(**state_vars)
        
        # 构建消息列表，系统提示词在前
        messages = [{"role": "system", "content": system_prompt}]
        
        # 如果state中有messages字段，则追加
        if "messages" in state:
            messages.extend(state["messages"])
            
        return messages
    except Exception as e:
        raise ValueError(f"Error applying template {template_name}: {e}")


def get_travel_planner_prompt(prompt_name: str, **kwargs) -> str:
    """
    获取旅行规划Agent的提示词模板
    
    Args:
        prompt_name: 提示词名称（travel_planner目录下的文件名，不含.md）
        **kwargs: 模板变量
        
    Returns:
        渲染后的提示词字符串
    """
    return get_prompt(f"travel_planner/{prompt_name}", **kwargs)


def list_available_templates(directory: str = "") -> List[str]:
    """
    列出可用的模板文件
    
    Args:
        directory: 子目录名称（如"travel_planner"）
        
    Returns:
        可用模板文件名列表（不含.md扩展名）
    """
    template_dir = PROMPT_ROOT / directory if directory else PROMPT_ROOT
    
    if not template_dir.exists():
        return []
    
    templates = []
    for file_path in template_dir.glob("*.md"):
        templates.append(file_path.stem)
    
    return sorted(templates)
