"""
TravelPlannerAgent LangGraph演示脚本

演示重构后的LangGraph实现的功能和兼容性。
"""

import asyncio
import json
import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.agents.travel_planner_agent_langgraph import TravelPlannerAgentLangGraph


async def demo_basic_planning():
    """演示基本规划功能"""
    print("=" * 60)
    print("演示1: 基本旅行规划功能")
    print("=" * 60)
    
    agent = TravelPlannerAgentLangGraph()
    
    # 示例用户画像
    user_profile = {
        "user_id": "demo_user_001",
        "age_group": "25-35",
        "travel_style": "深度游",
        "budget_level": "中等",
        "interests": ["文化历史", "美食", "自然风光"],
        "dietary_restrictions": [],
        "accessibility_needs": [],
        "preferred_languages": ["中文"]
    }
    
    query = "我想去北京玩3天，主要想看故宫、长城这些历史文化景点，还想尝尝北京烤鸭"
    
    print(f"用户查询: {query}")
    print(f"用户画像: {json.dumps(user_profile, ensure_ascii=False, indent=2)}")
    print("\n开始规划...")
    
    try:
        start_time = datetime.now()
        result = await agent.plan_travel(
            user_id="demo_user_001",
            query=query,
            user_profile=user_profile
        )
        end_time = datetime.now()
        
        print(f"\n规划完成! 耗时: {(end_time - start_time).total_seconds():.2f}秒")
        print(f"状态: {result.get('status')}")
        print(f"会话ID: {result.get('session_id')}")
        
        if result.get('status') == 'completed':
            # 显示核心意图分析结果
            if 'core_intent' in result:
                core_intent = result['core_intent']
                print(f"\n核心意图分析:")
                print(f"  目的地: {core_intent.get('destinations')}")
                print(f"  天数: {core_intent.get('days')}")
                print(f"  旅行主题: {core_intent.get('travel_theme')}")
                print(f"  置信度: {core_intent.get('confidence_score')}")
            
            # 显示行程摘要
            if 'summary' in result:
                summary = result['summary']
                print(f"\n行程摘要:")
                print(f"  总天数: {summary.get('total_days')}")
                print(f"  景点总数: {summary.get('total_attractions')}")
                print(f"  餐厅总数: {summary.get('total_restaurants')}")
                print(f"  预计总费用: {summary.get('estimated_total_cost')}")
            
            # 显示每日行程（简化）
            if 'daily_itineraries' in result:
                daily_itineraries = result['daily_itineraries']
                print(f"\n每日行程概览:")
                for day_itinerary in daily_itineraries[:2]:  # 只显示前2天
                    day = day_itinerary.get('day')
                    city = day_itinerary.get('city')
                    theme = day_itinerary.get('theme')
                    items = day_itinerary.get('items', [])
                    print(f"  第{day}天 ({city}) - {theme}: {len(items)}个安排")
        else:
            print(f"规划失败: {result.get('error_message')}")
    
    except Exception as e:
        print(f"演示失败: {str(e)}")


async def demo_multi_city_planning():
    """演示多城市规划功能"""
    print("\n" + "=" * 60)
    print("演示2: 多城市旅行规划功能")
    print("=" * 60)
    
    agent = TravelPlannerAgentLangGraph()
    
    user_profile = {
        "user_id": "demo_user_002",
        "age_group": "30-40",
        "travel_style": "打卡游",
        "budget_level": "舒适",
        "interests": ["现代建筑", "购物", "美食"]
    }
    
    query = "我想去北京和上海玩5天，主要想看现代建筑和购物，预算比较充足"
    
    print(f"用户查询: {query}")
    print("\n开始多城市规划...")
    
    try:
        result = await agent.plan_travel(
            user_id="demo_user_002",
            query=query,
            user_profile=user_profile
        )
        
        print(f"\n规划状态: {result.get('status')}")
        
        if result.get('status') == 'completed':
            # 显示多城市策略
            if 'multi_city_strategy' in result:
                strategy = result['multi_city_strategy']
                print(f"\n多城市策略:")
                print(f"  策略类型: {strategy.get('strategy_type')}")
                print(f"  推荐顺序: {strategy.get('recommended_order')}")
                print(f"  总交通时间: {strategy.get('total_travel_time')}")
                print(f"  预计交通费用: {strategy.get('estimated_transport_cost')}")
                print(f"  灵活性评分: {strategy.get('flexibility_score')}/10")
        
    except Exception as e:
        print(f"多城市演示失败: {str(e)}")


async def demo_driving_planning():
    """演示自驾规划功能"""
    print("\n" + "=" * 60)
    print("演示3: 自驾旅行规划功能")
    print("=" * 60)
    
    agent = TravelPlannerAgentLangGraph()
    
    user_profile = {
        "user_id": "demo_user_003",
        "age_group": "35-45",
        "travel_style": "自由行",
        "interests": ["自然风光", "户外活动"]
    }
    
    vehicle_info = {
        "vehicle_type": "electric",
        "brand": "特斯拉",
        "model": "Model Y",
        "nominal_range_km": 500,
        "charge_type": "fast",
        "vehicle_size": "suv"
    }
    
    query = "我想开电动车去杭州玩2天，主要想看西湖和周边的自然风光"
    
    print(f"用户查询: {query}")
    print(f"车辆信息: {json.dumps(vehicle_info, ensure_ascii=False, indent=2)}")
    print("\n开始自驾规划...")
    
    try:
        result = await agent.plan_travel(
            user_id="demo_user_003",
            query=query,
            user_profile=user_profile,
            vehicle_info=vehicle_info
        )
        
        print(f"\n规划状态: {result.get('status')}")
        print(f"规划模式: {result.get('planning_mode')}")
        
        if result.get('status') == 'completed':
            # 显示驾驶情境分析
            if 'driving_context' in result:
                context = result['driving_context']
                print(f"\n驾驶情境分析:")
                print(f"  驾驶策略: {context.get('driving_strategy')}")
                
                if 'range_planning' in context:
                    range_planning = context['range_planning']
                    print(f"  规划续航: {range_planning.get('planning_range_km')}km")
                    print(f"  保守系数: {range_planning.get('range_buffer_factor')}")
                
                if 'charging_strategy' in context:
                    charging = context['charging_strategy']
                    print(f"  充电策略: {charging.get('planning_mode')}")
        
    except Exception as e:
        print(f"自驾演示失败: {str(e)}")


async def demo_stream_planning():
    """演示流式规划功能"""
    print("\n" + "=" * 60)
    print("演示4: 流式旅行规划功能")
    print("=" * 60)
    
    agent = TravelPlannerAgentLangGraph()
    
    user_profile = {
        "user_id": "demo_user_004",
        "travel_style": "美食游",
        "interests": ["美食", "文化"]
    }
    
    query = "我想去成都玩3天，主要想吃火锅和各种小吃"
    
    print(f"用户查询: {query}")
    print("\n开始流式规划...")
    print("-" * 40)
    
    try:
        event_count = 0
        async for sse_event in agent.plan_travel_stream(
            user_id="demo_user_004",
            query=query,
            user_profile=user_profile
        ):
            event_count += 1
            
            # 解析事件
            lines = sse_event.strip().split('\n')
            event_type = None
            event_data = None
            
            for line in lines:
                if line.startswith('event:'):
                    event_type = line[6:].strip()
                elif line.startswith('data:'):
                    try:
                        event_data = json.loads(line[5:].strip())
                    except:
                        event_data = line[5:].strip()
            
            # 显示关键事件
            if event_type in ['stream_start', 'stage_progress', 'intent_analyzed', 
                            'preferences_analyzed', 'itinerary_generated', 
                            'planning_completed', 'stream_end', 'error']:
                print(f"[{event_type}] ", end="")
                if isinstance(event_data, dict):
                    if 'message' in event_data:
                        print(event_data['message'])
                    elif 'progress' in event_data:
                        print(f"进度: {event_data['progress']}%")
                    else:
                        print(f"数据: {event_data}")
                else:
                    print(event_data)
            
            # 限制演示事件数量
            if event_count > 50:
                print("... (限制演示长度)")
                break
        
        print("-" * 40)
        print(f"流式演示完成，共接收 {event_count} 个事件")
        
    except Exception as e:
        print(f"流式演示失败: {str(e)}")


async def demo_graph_visualization():
    """演示图形可视化功能"""
    print("\n" + "=" * 60)
    print("演示5: 工作流图形可视化")
    print("=" * 60)
    
    agent = TravelPlannerAgentLangGraph()
    
    try:
        mermaid_graph = agent.get_graph_visualization()
        print("LangGraph工作流图 (Mermaid格式):")
        print(mermaid_graph)
        
    except Exception as e:
        print(f"图形可视化演示失败: {str(e)}")


async def main():
    """主演示函数"""
    print("TravelPlannerAgent LangGraph 重构演示")
    print("=" * 60)
    print("本演示将展示重构后的LangGraph实现的各项功能")
    print("包括基本规划、多城市规划、自驾规划、流式处理等")
    print("=" * 60)
    
    try:
        # 运行各个演示
        await demo_basic_planning()
        await demo_multi_city_planning()
        await demo_driving_planning()
        await demo_stream_planning()
        await demo_graph_visualization()
        
        print("\n" + "=" * 60)
        print("所有演示完成！")
        print("重构后的LangGraph实现已准备就绪，可以替换原有的autogen实现。")
        print("=" * 60)
        
    except KeyboardInterrupt:
        print("\n演示被用户中断")
    except Exception as e:
        print(f"\n演示过程中发生错误: {str(e)}")


if __name__ == "__main__":
    # 运行演示
    asyncio.run(main())
