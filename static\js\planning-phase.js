/**
 * 规划阶段处理模块
 * 处理用户点击"立即规划"后的完整规划流程
 */

class PlanningPhaseManager {
    constructor() {
        this.currentSessionId = null;
        this.analysisResult = null;
        this.planningEventSource = null;
        this.planningStarted = false;
    }

    /**
     * 启动规划阶段
     * @param {string} sessionId - 会话ID
     * @param {Object} analysisResult - 分析阶段的结果
     */
    async startPlanningPhase(sessionId, analysisResult) {
        this.currentSessionId = sessionId;
        this.analysisResult = analysisResult;
        this.planningStarted = true;

        console.log('启动规划阶段:', { sessionId, analysisResult });

        try {
            // 显示规划阶段UI
            this.showPlanningUI();

            // TTS播报开始规划
            if (window.ttsManager) {
                window.ttsManager.speakStatusUpdate('开始规划您的精彩行程');
            }

            // 异步查询天气信息
            this.queryWeatherInfo();

            // 启动SSE连接
            await this.connectPlanningStream();

        } catch (error) {
            console.error('启动规划阶段失败:', error);
            this.showError('启动规划失败，请重试');
        }
    }

    /**
     * 连接规划阶段的SSE流
     */
    async connectPlanningStream() {
        const url = `/api/travel/plan/${this.currentSessionId}/start_planning`;
        
        try {
            // 发送POST请求启动规划
            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(this.analysisResult)
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            // 创建SSE连接读取流式响应
            const reader = response.body.getReader();
            const decoder = new TextDecoder();

            while (true) {
                const { done, value } = await reader.read();
                if (done) break;

                const chunk = decoder.decode(value);
                const lines = chunk.split('\n');

                for (const line of lines) {
                    if (line.startsWith('data: ')) {
                        try {
                            const eventData = JSON.parse(line.substring(6));
                            this.handlePlanningEvent(eventData);
                        } catch (e) {
                            console.warn('解析SSE事件失败:', line, e);
                        }
                    }
                }
            }

        } catch (error) {
            console.error('连接规划流失败:', error);
            this.showError('连接规划服务失败');
        }
    }

    /**
     * 处理规划事件
     * @param {Object} eventData - 事件数据
     */
    handlePlanningEvent(eventData) {
        console.log('收到规划事件:', eventData);

        const { event_type, payload } = eventData;

        switch (event_type) {
            case 'planning_started':
                this.handlePlanningStarted(payload);
                break;
            case 'stage_progress':
                this.handleStageProgress(payload);
                break;
            case 'weather_info':
                this.handleWeatherInfo(payload);
                break;
            case 'poi_search_progress':
                this.handlePOISearchProgress(payload);
                break;
            case 'poi_category_results':
                this.handlePOICategoryResults(payload);
                break;
            case 'daily_itinerary':
                this.handleDailyItinerary(payload);
                break;
            case 'itinerary_generated':
                this.handleItineraryGenerated(payload);
                break;
            case 'planning_completed':
                this.handlePlanningCompleted(payload);
                break;
            case 'tts_content':
                this.handleTTSContent(payload);
                break;
            case 'stream_start':
                this.handleStreamStart(payload);
                break;
            case 'stream_end':
                this.handleStreamEnd(payload);
                break;
            case 'error':
                this.handleError(payload);
                break;
            default:
                console.log('未处理的事件类型:', event_type, payload);
        }
    }

    /**
     * 处理规划启动事件
     */
    handlePlanningStarted(data) {
        this.updateProgress(data.progress, data.message);
        this.showPlanningMessage('🚀 开始规划您的精彩行程...');
    }

    /**
     * 处理阶段进度事件
     */
    handleStageProgress(data) {
        this.updateProgress(data.progress, data.message);
    }

    /**
     * 处理天气信息事件
     */
    handleWeatherInfo(data) {
        this.displayWeatherInfo(data.weather, data.description);
    }

    /**
     * 处理POI搜索进度事件
     */
    handlePOISearchProgress(data) {
        this.updateProgress(data.progress, data.message);
    }

    /**
     * 处理POI类别结果事件
     */
    handlePOICategoryResults(data) {
        this.displayPOIResults(data.category_name, data.pois, data.total_count);
    }

    /**
     * 处理每日行程事件
     */
    handleDailyItinerary(data) {
        this.displayDailyItinerary(data.day, data.itinerary);
    }

    /**
     * 处理规划完成事件
     */
    handlePlanningCompleted(data) {
        this.updateProgress(100, '🎉 行程规划完成！');
        this.showPlanningMessage('✨ 您的专属旅行方案已生成完成！');

        console.log('规划完成:', data);

        // 显示最终结果
        if (data.result) {
            this.displayFinalItinerary(data.result);
        } else if (data.summary) {
            // 如果没有完整结果，但有摘要，也显示
            this.displayItinerarySummary(data.summary);
        }

        // 更新主应用状态
        if (window.app) {
            window.app.currentPhase = 'completed';
            window.app.updateUI();
        }
    }

    /**
     * 处理流开始事件
     */
    handleStreamStart(data) {
        this.updateProgress(5, '🚀 开始规划您的精彩行程...');
        this.showPlanningMessage('🚀 开始规划您的精彩行程...');
    }

    /**
     * 处理流结束事件
     */
    handleStreamEnd(data) {
        this.updateProgress(95, '🎯 规划流程即将完成...');
    }

    /**
     * 处理行程生成事件
     */
    handleItineraryGenerated(data) {
        this.updateProgress(80, '📋 行程方案生成完成');
        this.showPlanningMessage('📋 行程方案已生成，正在进行最终优化...');

        // 如果有行程数据，显示预览
        if (data.daily_count) {
            this.showPlanningMessage(`📅 已生成 ${data.daily_count} 天的详细行程`);
        }
    }

    /**
     * 处理TTS内容事件
     */
    handleTTSContent(data) {
        if (window.ttsManager) {
            window.ttsManager.speak(data.content, data.priority);
        }
    }

    /**
     * 处理错误事件
     */
    handleError(data) {
        console.error('规划错误:', data);
        this.showError(data.error_message || data.error || '规划过程中发生错误');
    }

    /**
     * 显示规划阶段UI
     */
    showPlanningUI() {
        // 保持左侧分析面板可见（显示已完成的意图理解）
        const analysisPanel = document.querySelector('.analysis-panel');
        if (analysisPanel) {
            analysisPanel.style.display = 'block';
        }

        // 在右侧面板显示规划内容
        const itineraryView = document.getElementById('itineraryView');
        if (itineraryView) {
            itineraryView.style.display = 'block';
            this.createPlanningContent(itineraryView);
        }
    }

    /**
     * 创建规划面板
     */
    createPlanningPanel() {
        const container = document.querySelector('.container');
        if (!container) return;

        const planningPanel = document.createElement('div');
        planningPanel.className = 'planning-panel';
        planningPanel.innerHTML = `
            <div class="planning-header">
                <h2>🗺️ 行程规划中</h2>
                <div class="progress-container">
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 0%"></div>
                    </div>
                    <div class="progress-text">准备开始...</div>
                </div>
            </div>
            <div class="planning-content">
                <div class="weather-section" style="display: none;">
                    <h3>🌤️ 天气信息</h3>
                    <div class="weather-info"></div>
                </div>
                <div class="poi-section" style="display: none;">
                    <h3>📍 精选推荐</h3>
                    <div class="poi-results"></div>
                </div>
                <div class="itinerary-section" style="display: none;">
                    <h3>📅 详细行程</h3>
                    <div class="daily-itineraries"></div>
                </div>
                <div class="planning-messages">
                    <div class="message-list"></div>
                </div>
            </div>
        `;

        container.appendChild(planningPanel);
    }

    /**
     * 查询天气信息
     */
    async queryWeatherInfo() {
        try {
            if (!this.analysisResult || !this.analysisResult.core_intent) {
                console.warn('缺少分析结果，无法查询天气');
                return;
            }

            const destinations = this.analysisResult.core_intent.destinations || [];
            if (destinations.length === 0) {
                console.warn('没有目的地信息，无法查询天气');
                return;
            }

            const destination = destinations[0]; // 取第一个目的地
            console.log('查询天气信息:', destination);

            // 显示天气查询进度
            this.showPlanningMessage(`🌤️ 正在查询${destination}的天气信息...`);

            // 调用天气API
            const response = await fetch('/api/weather/forecast', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    city: destination,
                    days: this.analysisResult.core_intent.days || 3
                })
            });

            if (response.ok) {
                const weatherData = await response.json();
                this.displayWeatherInfo(weatherData.weather, weatherData.description);
                this.showPlanningMessage(`✅ ${destination}天气信息已获取`);
            } else {
                console.warn('天气查询失败:', response.status);
                this.showPlanningMessage(`⚠️ ${destination}天气信息暂时无法获取`);
            }
        } catch (error) {
            console.error('天气查询错误:', error);
            this.showPlanningMessage('⚠️ 天气信息查询失败，将基于历史数据规划');
        }
    }

    /**
     * 在指定容器中创建规划内容
     */
    createPlanningContent(container) {
        if (!container) return;

        container.innerHTML = `
            <div class="planning-content-wrapper">
                <div class="planning-header">
                    <h4 class="planning-title">
                        <i class="bi bi-map"></i>
                        🚀 开始规划您的精彩行程...
                    </h4>
                    <div class="progress-container">
                        <div class="progress">
                            <div class="progress-bar bg-primary" role="progressbar" style="width: 0%" id="planningProgressBar"></div>
                        </div>
                        <div class="progress-text mt-2" id="planningProgressText">准备开始...</div>
                    </div>
                </div>

                <div class="planning-content-body">
                    <div class="weather-section" style="display: none;">
                        <div class="card mb-3">
                            <div class="card-header">
                                <h6 class="mb-0">🌤️ 天气信息</h6>
                            </div>
                            <div class="card-body">
                                <div class="weather-info"></div>
                            </div>
                        </div>
                    </div>

                    <div class="poi-section" style="display: none;">
                        <div class="card mb-3">
                            <div class="card-header">
                                <h6 class="mb-0">📍 精选推荐</h6>
                            </div>
                            <div class="card-body">
                                <div class="poi-results"></div>
                            </div>
                        </div>
                    </div>

                    <div class="itinerary-section" style="display: none;">
                        <div class="card mb-3">
                            <div class="card-header">
                                <h6 class="mb-0">📅 详细行程</h6>
                            </div>
                            <div class="card-body">
                                <div class="daily-itineraries"></div>
                            </div>
                        </div>
                    </div>

                    <div class="planning-messages">
                        <div class="message-item">
                            <i class="bi bi-info-circle text-primary me-2"></i>
                            <span>准备开始规划...</span>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 更新进度
     */
    updateProgress(progress, message) {
        // 尝试使用新的进度条ID
        const progressBar = document.getElementById('planningProgressBar');
        const progressText = document.getElementById('planningProgressText');

        // 如果新的元素不存在，回退到旧的选择器
        const fallbackProgressFill = document.querySelector('.progress-fill');
        const fallbackProgressText = document.querySelector('.progress-text');

        if (progressBar) {
            progressBar.style.width = `${progress}%`;
            progressBar.setAttribute('aria-valuenow', progress);
        } else if (fallbackProgressFill) {
            fallbackProgressFill.style.width = `${progress}%`;
        }

        if (progressText) {
            progressText.textContent = message || `${progress}%`;
        } else if (fallbackProgressText) {
            fallbackProgressText.textContent = message || `${progress}%`;
        }
    }

    /**
     * 显示规划消息
     */
    showPlanningMessage(message) {
        const messageList = document.querySelector('.message-list');
        if (!messageList) return;

        const messageElement = document.createElement('div');
        messageElement.className = 'planning-message';
        messageElement.innerHTML = `
            <span class="message-time">${new Date().toLocaleTimeString()}</span>
            <span class="message-content">${message}</span>
        `;

        messageList.appendChild(messageElement);
        messageList.scrollTop = messageList.scrollHeight;
    }

    /**
     * 显示天气信息
     */
    displayWeatherInfo(weather, description) {
        const weatherSection = document.querySelector('.weather-section');
        const weatherInfo = document.querySelector('.weather-info');

        if (weatherSection && weatherInfo) {
            weatherSection.style.display = 'block';
            weatherInfo.innerHTML = `
                <div class="weather-card">
                    <div class="weather-main">${weather.weather}</div>
                    <div class="weather-temp">${weather.temperature}</div>
                    <div class="weather-desc">${description}</div>
                </div>
            `;
        }
    }

    /**
     * 显示POI结果
     */
    displayPOIResults(categoryName, pois, totalCount) {
        const poiSection = document.querySelector('.poi-section');
        const poiResults = document.querySelector('.poi-results');

        if (poiSection && poiResults) {
            poiSection.style.display = 'block';

            const categoryDiv = document.createElement('div');
            categoryDiv.className = 'poi-category';
            categoryDiv.innerHTML = `
                <h4>${categoryName} (${totalCount}个)</h4>
                <div class="poi-list">
                    ${pois.map(poi => this.createPOICard(poi)).join('')}
                </div>
            `;

            poiResults.appendChild(categoryDiv);
        }
    }

    /**
     * 创建POI卡片
     */
    createPOICard(poi) {
        return `
            <div class="poi-card">
                <div class="poi-header">
                    <h5>${poi.name}</h5>
                    <span class="poi-rating">⭐ ${poi.rating}</span>
                </div>
                <div class="poi-address">${poi.address}</div>
                <div class="poi-description">${poi.description}</div>
                <div class="poi-details">
                    <span class="poi-price">${poi.price.value}</span>
                    <span class="poi-hours">${poi.business_hours}</span>
                </div>
                <div class="poi-tags">
                    ${poi.tags.map(tag => `<span class="tag">${tag}</span>`).join('')}
                </div>
            </div>
        `;
    }

    /**
     * 显示每日行程
     */
    displayDailyItinerary(day, itinerary) {
        const itinerarySection = document.querySelector('.itinerary-section');
        const dailyItineraries = document.querySelector('.daily-itineraries');

        if (itinerarySection && dailyItineraries) {
            itinerarySection.style.display = 'block';

            const dayDiv = document.createElement('div');
            dayDiv.className = 'day-itinerary';
            dayDiv.innerHTML = `
                <h4>第${day}天 - ${itinerary.theme}</h4>
                <div class="itinerary-items">
                    ${itinerary.items.map(item => this.createItineraryItem(item)).join('')}
                </div>
                <div class="day-summary">
                    <span>总距离: ${itinerary.total_distance}km</span>
                    <span>预计用时: ${itinerary.estimated_duration}</span>
                </div>
            `;

            dailyItineraries.appendChild(dayDiv);
        }
    }

    /**
     * 创建行程项目
     */
    createItineraryItem(item) {
        const poi = item.poi || {};
        return `
            <div class="itinerary-item">
                <div class="item-time">${item.start_time} - ${item.end_time}</div>
                <div class="item-content">
                    <h6>${poi.name || item.description}</h6>
                    <p>${item.description}</p>
                    <span class="item-duration">${item.duration}</span>
                </div>
            </div>
        `;
    }

    /**
     * 显示规划完成
     */
    showPlanningComplete(result, summary) {
        this.showPlanningMessage('🎉 行程规划完成！');
        
        // 可以在这里添加更多完成后的处理逻辑
        console.log('规划完成:', { result, summary });
    }

    /**
     * 显示错误
     */
    showError(message) {
        this.showPlanningMessage(`❌ ${message}`);
        
        // 可以添加错误处理UI
        const errorDiv = document.createElement('div');
        errorDiv.className = 'error-message';
        errorDiv.textContent = message;
        
        const container = document.querySelector('.planning-content');
        if (container) {
            container.appendChild(errorDiv);
        }
    }

    /**
     * 显示最终行程结果
     */
    displayFinalItinerary(result) {
        console.log('显示最终行程结果:', result);

        // 检查数据结构并显示行程结果
        if (result && (result.itinerary || result.daily_itineraries)) {
            // 创建卡片式的行程显示
            this.createItineraryCards(result);

            // 通知主应用显示行程结果
            if (window.app) {
                window.app.currentItinerary = result;
                window.app.currentPhase = 'completed';
                window.app.updateUI();
            }
        } else {
            console.warn('行程数据格式不正确:', result);
        }
    }

    /**
     * 创建卡片式行程显示
     */
    createItineraryCards(result) {
        const itineraryView = document.getElementById('itineraryView');
        if (!itineraryView) return;

        const dailyItineraries = result.daily_itineraries || result.itinerary || [];
        const summary = result.summary || {};

        let cardsHtml = `
            <div class="itinerary-cards-container">
                <div class="itinerary-header">
                    <h3 class="itinerary-title">
                        <i class="bi bi-map"></i>
                        ${summary.title || '您的专属旅行方案'}
                    </h3>
                    <p class="itinerary-description">
                        ${summary.description || `${result.destinations?.join('、') || '精彩'}之旅 · ${dailyItineraries.length}天行程`}
                    </p>
                </div>

                <div class="daily-itineraries">
        `;

        // 生成每日行程卡片
        dailyItineraries.forEach((day, index) => {
            cardsHtml += this.generateDayCard(day, index + 1);
        });

        cardsHtml += `
                </div>

                <div class="itinerary-actions">
                    <button class="btn btn-outline-danger" onclick="window.app.cancelPlanning()">
                        <i class="bi bi-x-circle"></i>
                        取消生成
                    </button>
                    <button class="btn btn-success" onclick="window.app.regenerateItinerary()">
                        <i class="bi bi-arrow-clockwise"></i>
                        重新生成
                    </button>
                    <button class="btn btn-primary" onclick="window.app.viewItinerary()">
                        <i class="bi bi-eye"></i>
                        查看行程
                    </button>
                </div>
            </div>
        `;

        itineraryView.innerHTML = cardsHtml;

        // 异步加载POI图片
        this.loadPOIImages(dailyItineraries);
    }

    /**
     * 生成单日行程卡片
     */
    generateDayCard(day, dayNumber) {
        console.log(`生成第${dayNumber}天卡片，数据:`, day);

        const dayTitle = day.theme || `DAY${dayNumber}精彩一日`;

        // 处理不同的数据结构
        let activities = [];

        if (day.schedule) {
            activities = day.schedule;
        } else if (day.activities) {
            activities = day.activities;
        } else if (day.timings) {
            activities = day.timings;
        } else if (day[' timings']) {
            // 处理带空格的字段名
            activities = day[' timings'];
        } else if (day.items) {
            activities = day.items;
        } else if (day['上午'] || day['下午'] || day['晚上']) {
            // 处理按时段分组的数据结构
            activities = [];
            if (day['上午']) {
                activities.push({
                    time: day['上午'].time,
                    location: day['上午'].location,
                    activity: day['上午'].activity,
                    details: day['上午'].details
                });
            }
            if (day['下午']) {
                activities.push({
                    time: day['下午'].time,
                    location: day['下午'].location,
                    activity: day['下午'].activity,
                    details: day['下午'].details
                });
            }
            if (day['晚上']) {
                activities.push({
                    time: day['晚上'].time,
                    location: day['晚上'].location,
                    activity: day['晚上'].activity,
                    details: day['晚上'].details
                });
            }
        }

        console.log(`第${dayNumber}天活动数据:`, activities);

        // 按时间排序活动
        activities.sort((a, b) => {
            const timeA = this.parseTime(a.time);
            const timeB = this.parseTime(b.time);
            return timeA - timeB;
        });

        let activitiesHtml = '';
        activities.forEach((activity, index) => {
            // 提取位置信息
            const locationName = activity.location || activity.name || activity.place || '精彩活动';
            const activityId = `activity-${dayNumber}-${index}`;

            // 提取活动描述
            const activityDesc = activity.activity ||
                                activity.description ||
                                activity.details ||
                                activity.activities ||
                                activity.content ||
                                '';

            // 提取时间信息
            const timeInfo = activity.time ||
                           (activity.start && activity.end ? `${activity.start}-${activity.end}` : '') ||
                           '全天';

            activitiesHtml += `
                <div class="activity-item" id="${activityId}">
                    <div class="activity-image">
                        <div class="image-placeholder" data-location="${locationName}" data-activity-id="${activityId}">
                            <i class="bi bi-image"></i>
                            <span>加载中...</span>
                        </div>
                    </div>
                    <div class="activity-time">${timeInfo}</div>
                    <div class="activity-content">
                        <div class="activity-name">${locationName}</div>
                        <div class="activity-desc">${activityDesc}</div>
                        ${this.formatTransportInfo(activity.transportation || activity.transport)}
                    </div>
                </div>
            `;
        });

        return `
            <div class="daily-itinerary">
                <div class="day-header">
                    <div class="day-number">${dayNumber}</div>
                    <div class="day-info">
                        <h4 class="day-title">${dayTitle}</h4>
                        <p class="day-date">${day.date || `第${dayNumber}天`}</p>
                    </div>
                </div>
                <div class="day-activities">
                    ${activitiesHtml || '<p class="text-muted">暂无具体安排</p>'}
                </div>
                ${day.dining ? `
                    <div class="day-dining">
                        <h6><i class="bi bi-cup-hot"></i> 推荐美食</h6>
                        <p>午餐: ${day.dining.lunch || '自由选择'}</p>
                        <p>晚餐: ${day.dining.dinner || '自由选择'}</p>
                    </div>
                ` : ''}
            </div>
        `;
    }

    /**
     * 异步加载POI图片
     */
    async loadPOIImages(dailyItineraries) {
        console.log('开始加载POI图片...');

        for (let dayIndex = 0; dayIndex < dailyItineraries.length; dayIndex++) {
            const day = dailyItineraries[dayIndex];

            // 处理不同的数据结构
            let activities = [];
            if (day.schedule) {
                activities = day.schedule;
            } else if (day.activities) {
                activities = day.activities;
            } else if (day.timings) {
                activities = day.timings;
            } else if (day[' timings']) {
                // 处理带空格的字段名
                activities = day[' timings'];
            } else if (day.items) {
                activities = day.items;
            }

            console.log(`第${dayIndex + 1}天POI图片加载，活动数量: ${activities.length}`);

            for (let actIndex = 0; actIndex < activities.length; actIndex++) {
                const activity = activities[actIndex];

                // 调试：打印活动数据结构
                console.log(`第${dayIndex + 1}天活动${actIndex + 1}数据:`, activity);

                // 修复：从正确的数据结构中提取location信息
                let locationName = null;

                // 尝试多种数据结构
                if (activity.poi && activity.poi.name) {
                    locationName = activity.poi.name;
                } else if (activity.poi && activity.poi.location) {
                    locationName = activity.poi.location;
                } else if (activity.location) {
                    locationName = activity.location;
                } else if (activity.name) {
                    locationName = activity.name;
                } else if (activity.place) {
                    locationName = activity.place;
                }

                const activityId = `activity-${dayIndex + 1}-${actIndex}`;

                console.log(`提取的location: ${locationName}, poi对象:`, activity.poi);

                if (locationName) {
                    console.log(`准备加载POI图片: ${locationName}, ID: ${activityId}`);
                    // 添加延迟避免API调用过于频繁
                    setTimeout(() => {
                        this.loadSinglePOIImage(locationName, activityId);
                    }, actIndex * 1000); // 每个请求间隔1秒
                } else {
                    console.log(`跳过活动${actIndex + 1}，没有location信息`);
                }
            }
        }
    }

    /**
     * 加载单个POI的图片
     */
    async loadSinglePOIImage(locationName, activityId) {
        try {
            console.log(`查询POI图片: ${locationName}`);

            const response = await fetch('/api/map/search_pois', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    keywords: locationName,
                    city: this.analysisResult?.core_intent?.destinations?.[0] || '',
                    type: '',
                    page_size: 1
                })
            });

            if (response.ok) {
                const data = await response.json();
                if (data.pois && data.pois.length > 0) {
                    const poi = data.pois[0];
                    this.updatePOIImage(activityId, poi);
                } else {
                    this.updatePOIImagePlaceholder(activityId, '暂无图片');
                }
            } else {
                console.warn(`POI查询失败: ${locationName}`, response.status);
                this.updatePOIImagePlaceholder(activityId, '图片加载失败');
            }
        } catch (error) {
            console.error(`POI图片加载错误: ${locationName}`, error);
            this.updatePOIImagePlaceholder(activityId, '图片加载失败');
        }
    }

    /**
     * 更新POI图片
     */
    updatePOIImage(activityId, poi) {
        const placeholder = document.querySelector(`#${activityId} .image-placeholder`);
        if (!placeholder) return;

        console.log(`更新POI图片: ${poi.name}`, poi);

        // 检查POI是否有图片
        const imageUrl = poi.photos?.[0] || poi.image || null;

        if (imageUrl) {
            console.log(`设置图片URL: ${imageUrl}`);

            // 创建图片元素
            const img = document.createElement('img');
            img.src = imageUrl;
            img.alt = poi.name || '景点图片';
            img.className = 'poi-image';
            img.style.width = '100%';
            img.style.height = '100%';
            img.style.objectFit = 'cover';
            img.style.borderRadius = '8px';

            // 图片加载成功
            img.onload = () => {
                console.log(`图片加载成功: ${poi.name}`);
                placeholder.innerHTML = '';
                placeholder.appendChild(img);
            };

            // 图片加载失败，显示POI详细信息
            img.onerror = () => {
                console.log(`图片加载失败，显示POI信息: ${poi.name}`);
                this.showPOIDetails(placeholder, poi);
            };

        } else {
            console.log(`没有图片，显示POI详细信息: ${poi.name}`);
            this.showPOIDetails(placeholder, poi);
        }
    }

    /**
     * 显示POI详细信息
     */
    showPOIDetails(placeholder, poi) {
        // 处理不同的POI类型，显示相应的详细信息
        const isRestaurant = poi.type && (poi.type.includes('餐饮') || poi.type.includes('美食') || poi.type.includes('餐厅'));
        const isAttraction = poi.type && (poi.type.includes('景点') || poi.type.includes('旅游') || poi.type.includes('文化'));

        placeholder.innerHTML = `
            <div class="poi-info">
                <div class="poi-header">
                    <i class="bi ${isRestaurant ? 'bi-cup-hot-fill' : isAttraction ? 'bi-camera-fill' : 'bi-geo-alt-fill'} text-primary"></i>
                    <div class="poi-name">${poi.name || '未知地点'}</div>
                </div>
                <div class="poi-details">
                    ${poi.address ? `<div class="poi-address"><i class="bi bi-pin-map"></i> ${poi.address}</div>` : ''}
                    ${poi.rating && poi.rating > 0 ? `<div class="poi-rating"><i class="bi bi-star-fill text-warning"></i> ${poi.rating}分</div>` : ''}
                    ${poi.type ? `<div class="poi-type"><i class="bi bi-tag"></i> ${poi.type}</div>` : ''}
                    ${poi.business_hours ? `<div class="poi-hours"><i class="bi bi-clock"></i> ${poi.business_hours}</div>` : ''}
                    ${poi.phone ? `<div class="poi-phone"><i class="bi bi-telephone"></i> ${poi.phone}</div>` : ''}
                    ${poi.price_range ? `<div class="poi-price"><i class="bi bi-currency-yen"></i> ${poi.price_range}</div>` : ''}
                    ${poi.price && poi.price > 0 ? `<div class="poi-price"><i class="bi bi-currency-yen"></i> ¥${poi.price}</div>` : ''}
                    ${isRestaurant ? this.getRestaurantInfo(poi) : ''}
                    ${isAttraction ? this.getAttractionInfo(poi) : ''}
                </div>
            </div>
        `;
    }

    /**
     * 获取餐厅特色信息
     */
    getRestaurantInfo(poi) {
        let info = '';

        // 根据POI类型推断菜系
        if (poi.type) {
            if (poi.type.includes('中餐') || poi.name.includes('酒楼') || poi.name.includes('茶餐厅')) {
                info += `<div class="poi-cuisine"><i class="bi bi-bowl"></i> 中式菜系</div>`;
            } else if (poi.type.includes('西餐') || poi.name.includes('咖啡') || poi.name.includes('Pizza')) {
                info += `<div class="poi-cuisine"><i class="bi bi-cup-straw"></i> 西式菜系</div>`;
            } else if (poi.name.includes('海鲜') || poi.name.includes('鱼')) {
                info += `<div class="poi-cuisine"><i class="bi bi-water"></i> 海鲜菜系</div>`;
            } else if (poi.name.includes('火锅') || poi.name.includes('烧烤')) {
                info += `<div class="poi-cuisine"><i class="bi bi-fire"></i> 特色菜系</div>`;
            }
        }

        // 根据名称推断特色
        if (poi.name.includes('老字号') || poi.name.includes('传统')) {
            info += `<div class="poi-feature"><i class="bi bi-award"></i> 传统老店</div>`;
        } else if (poi.name.includes('网红') || poi.name.includes('时尚')) {
            info += `<div class="poi-feature"><i class="bi bi-heart"></i> 网红店铺</div>`;
        }

        return info;
    }

    /**
     * 获取景点特色信息
     */
    getAttractionInfo(poi) {
        let info = '';

        // 根据POI类型推断景点特色
        if (poi.type) {
            if (poi.type.includes('寺庙') || poi.type.includes('宗教')) {
                info += `<div class="poi-feature"><i class="bi bi-building"></i> 宗教文化</div>`;
            } else if (poi.type.includes('博物馆') || poi.type.includes('文化')) {
                info += `<div class="poi-feature"><i class="bi bi-book"></i> 文化教育</div>`;
            } else if (poi.type.includes('公园') || poi.type.includes('自然')) {
                info += `<div class="poi-feature"><i class="bi bi-tree"></i> 自然风光</div>`;
            } else if (poi.type.includes('古迹') || poi.type.includes('历史')) {
                info += `<div class="poi-feature"><i class="bi bi-hourglass"></i> 历史古迹</div>`;
            }
        }

        // 推荐游览时间
        if (poi.name.includes('博物馆') || poi.name.includes('展览')) {
            info += `<div class="poi-time"><i class="bi bi-stopwatch"></i> 建议2-3小时</div>`;
        } else if (poi.name.includes('公园') || poi.name.includes('岛')) {
            info += `<div class="poi-time"><i class="bi bi-stopwatch"></i> 建议半天</div>`;
        } else {
            info += `<div class="poi-time"><i class="bi bi-stopwatch"></i> 建议1-2小时</div>`;
        }

        return info;
    }

    /**
     * 更新POI图片占位符
     */
    updatePOIImagePlaceholder(activityId, message) {
        const placeholder = document.querySelector(`#${activityId} .image-placeholder`);
        if (placeholder) {
            placeholder.innerHTML = `
                <i class="bi bi-image"></i>
                <span>${message}</span>
            `;
        }
    }

    /**
     * 显示天气信息
     */
    displayWeatherInfo(weatherData, description) {
        console.log('显示天气信息:', weatherData, description);

        const weatherSection = document.querySelector('.weather-section');
        if (!weatherSection) return;

        weatherSection.style.display = 'block';
        const weatherInfo = weatherSection.querySelector('.weather-info');

        if (weatherData && weatherData.length > 0) {
            let weatherHtml = '';
            weatherData.forEach(day => {
                weatherHtml += `
                    <div class="weather-day">
                        <div class="date">${day.date || '未知日期'}</div>
                        <div class="temp">${day.temperature || day.temp || '未知温度'}</div>
                        <div class="desc">${day.weather || day.description || '未知天气'}</div>
                    </div>
                `;
            });
            weatherInfo.innerHTML = weatherHtml;
        } else {
            weatherInfo.innerHTML = `
                <div class="weather-day">
                    <div class="date">天气信息</div>
                    <div class="temp">--°C</div>
                    <div class="desc">${description || '暂时无法获取'}</div>
                </div>
            `;
        }
    }

    /**
     * 显示规划消息
     */
    showPlanningMessage(message) {
        const messagesContainer = document.querySelector('.planning-messages');
        if (!messagesContainer) return;

        const messageItem = document.createElement('div');
        messageItem.className = 'message-item';
        messageItem.innerHTML = `
            <i class="bi bi-info-circle text-primary me-2"></i>
            <span>${message}</span>
        `;

        messagesContainer.appendChild(messageItem);

        // 滚动到最新消息
        messageItem.scrollIntoView({ behavior: 'smooth' });
    }

    /**
     * 解析时间字符串为数字（用于排序）
     */
    parseTime(timeStr) {
        if (!timeStr || timeStr === '全天') {
            return 0; // 全天活动排在最前面
        }

        // 提取开始时间，格式如 "08:30-09:30" 或 "08:30"
        const match = timeStr.match(/(\d{1,2}):(\d{2})/);
        if (match) {
            const hours = parseInt(match[1]);
            const minutes = parseInt(match[2]);
            return hours * 60 + minutes; // 转换为分钟数
        }

        return 999; // 无法解析的时间排在最后
    }

    /**
     * 格式化交通信息
     */
    formatTransportInfo(transport) {
        if (!transport) return '';

        if (typeof transport === 'string') {
            return `<div class="activity-transport"><i class="bi bi-car-front"></i> ${transport}</div>`;
        }

        if (typeof transport === 'object') {
            let transportText = '';
            if (transport.mode) {
                transportText += transport.mode;
            }
            if (transport.duration) {
                transportText += ` (${transport.duration})`;
            }
            if (transport.distance) {
                transportText += ` ${transport.distance}`;
            }
            if (transport.description) {
                transportText += ` - ${transport.description}`;
            }

            if (transportText) {
                return `<div class="activity-transport"><i class="bi bi-car-front"></i> ${transportText}</div>`;
            }
        }

        return '';
    }

    /**
     * 显示行程摘要
     */
    displayItinerarySummary(summary) {
        console.log('显示行程摘要:', summary);

        // 创建摘要显示
        const summaryHtml = `
            <div class="itinerary-summary">
                <h4>行程摘要</h4>
                <p>目的地: ${summary.destinations?.join(', ') || '未知'}</p>
                <p>天数: ${summary.days || '未知'}</p>
                <p>总预算: ${summary.total_budget || '待估算'}</p>
            </div>
        `;

        // 显示在结果区域
        const resultContainer = document.querySelector('.itinerary-result');
        if (resultContainer) {
            resultContainer.innerHTML = summaryHtml;
        }
    }

    /**
     * 停止规划
     */
    stopPlanning() {
        if (this.planningEventSource) {
            this.planningEventSource.close();
            this.planningEventSource = null;
        }
        this.planningStarted = false;
    }
}

// 创建全局实例
window.planningPhaseManager = new PlanningPhaseManager();

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = PlanningPhaseManager;
}
