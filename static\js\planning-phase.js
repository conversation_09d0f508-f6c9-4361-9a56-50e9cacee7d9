/**
 * 规划阶段处理模块
 * 处理用户点击"立即规划"后的完整规划流程
 */

class PlanningPhaseManager {
    constructor() {
        this.currentSessionId = null;
        this.analysisResult = null;
        this.planningEventSource = null;
        this.planningStarted = false;
    }

    /**
     * 启动规划阶段
     * @param {string} sessionId - 会话ID
     * @param {Object} analysisResult - 分析阶段的结果
     */
    async startPlanningPhase(sessionId, analysisResult) {
        this.currentSessionId = sessionId;
        this.analysisResult = analysisResult;
        this.planningStarted = true;

        console.log('启动规划阶段:', { sessionId, analysisResult });

        try {
            // 显示规划阶段UI
            this.showPlanningUI();

            // 启动SSE连接
            await this.connectPlanningStream();

        } catch (error) {
            console.error('启动规划阶段失败:', error);
            this.showError('启动规划失败，请重试');
        }
    }

    /**
     * 连接规划阶段的SSE流
     */
    async connectPlanningStream() {
        const url = `/api/travel/plan/${this.currentSessionId}/start_planning`;
        
        try {
            // 发送POST请求启动规划
            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(this.analysisResult)
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            // 创建SSE连接读取流式响应
            const reader = response.body.getReader();
            const decoder = new TextDecoder();

            while (true) {
                const { done, value } = await reader.read();
                if (done) break;

                const chunk = decoder.decode(value);
                const lines = chunk.split('\n');

                for (const line of lines) {
                    if (line.startsWith('data: ')) {
                        try {
                            const eventData = JSON.parse(line.substring(6));
                            this.handlePlanningEvent(eventData);
                        } catch (e) {
                            console.warn('解析SSE事件失败:', line, e);
                        }
                    }
                }
            }

        } catch (error) {
            console.error('连接规划流失败:', error);
            this.showError('连接规划服务失败');
        }
    }

    /**
     * 处理规划事件
     * @param {Object} eventData - 事件数据
     */
    handlePlanningEvent(eventData) {
        console.log('收到规划事件:', eventData);

        const { event_type, payload } = eventData;

        switch (event_type) {
            case 'planning_started':
                this.handlePlanningStarted(payload);
                break;
            case 'stage_progress':
                this.handleStageProgress(payload);
                break;
            case 'weather_info':
                this.handleWeatherInfo(payload);
                break;
            case 'poi_search_progress':
                this.handlePOISearchProgress(payload);
                break;
            case 'poi_category_results':
                this.handlePOICategoryResults(payload);
                break;
            case 'daily_itinerary':
                this.handleDailyItinerary(payload);
                break;
            case 'itinerary_generated':
                this.handleItineraryGenerated(payload);
                break;
            case 'planning_completed':
                this.handlePlanningCompleted(payload);
                break;
            case 'tts_content':
                this.handleTTSContent(payload);
                break;
            case 'stream_start':
                this.handleStreamStart(payload);
                break;
            case 'stream_end':
                this.handleStreamEnd(payload);
                break;
            case 'error':
                this.handleError(payload);
                break;
            default:
                console.log('未处理的事件类型:', event_type, payload);
        }
    }

    /**
     * 处理规划启动事件
     */
    handlePlanningStarted(data) {
        this.updateProgress(data.progress, data.message);
        this.showPlanningMessage('🚀 开始规划您的精彩行程...');
    }

    /**
     * 处理阶段进度事件
     */
    handleStageProgress(data) {
        this.updateProgress(data.progress, data.message);
    }

    /**
     * 处理天气信息事件
     */
    handleWeatherInfo(data) {
        this.displayWeatherInfo(data.weather, data.description);
    }

    /**
     * 处理POI搜索进度事件
     */
    handlePOISearchProgress(data) {
        this.updateProgress(data.progress, data.message);
    }

    /**
     * 处理POI类别结果事件
     */
    handlePOICategoryResults(data) {
        this.displayPOIResults(data.category_name, data.pois, data.total_count);
    }

    /**
     * 处理每日行程事件
     */
    handleDailyItinerary(data) {
        this.displayDailyItinerary(data.day, data.itinerary);
    }

    /**
     * 处理规划完成事件
     */
    handlePlanningCompleted(data) {
        this.updateProgress(100, '🎉 行程规划完成！');
        this.showPlanningMessage('✨ 您的专属旅行方案已生成完成！');

        console.log('规划完成:', data);

        // 显示最终结果
        if (data.result) {
            this.displayFinalItinerary(data.result);
        } else if (data.summary) {
            // 如果没有完整结果，但有摘要，也显示
            this.displayItinerarySummary(data.summary);
        }

        // 更新主应用状态
        if (window.app) {
            window.app.currentPhase = 'completed';
            window.app.updateUI();
        }
    }

    /**
     * 处理流开始事件
     */
    handleStreamStart(data) {
        this.updateProgress(5, '🚀 开始规划您的精彩行程...');
        this.showPlanningMessage('🚀 开始规划您的精彩行程...');
    }

    /**
     * 处理流结束事件
     */
    handleStreamEnd(data) {
        this.updateProgress(95, '🎯 规划流程即将完成...');
    }

    /**
     * 处理行程生成事件
     */
    handleItineraryGenerated(data) {
        this.updateProgress(80, '📋 行程方案生成完成');
        this.showPlanningMessage('📋 行程方案已生成，正在进行最终优化...');

        // 如果有行程数据，显示预览
        if (data.daily_count) {
            this.showPlanningMessage(`📅 已生成 ${data.daily_count} 天的详细行程`);
        }
    }

    /**
     * 处理TTS内容事件
     */
    handleTTSContent(data) {
        if (window.ttsManager) {
            window.ttsManager.speak(data.content, data.priority);
        }
    }

    /**
     * 处理错误事件
     */
    handleError(data) {
        console.error('规划错误:', data);
        this.showError(data.error_message || data.error || '规划过程中发生错误');
    }

    /**
     * 显示规划阶段UI
     */
    showPlanningUI() {
        // 隐藏分析阶段UI
        const analysisPanel = document.querySelector('.analysis-panel');
        if (analysisPanel) {
            analysisPanel.style.display = 'none';
        }

        // 显示规划阶段UI
        const planningPanel = document.querySelector('.planning-panel');
        if (planningPanel) {
            planningPanel.style.display = 'block';
        } else {
            // 如果不存在规划面板，创建一个
            this.createPlanningPanel();
        }
    }

    /**
     * 创建规划面板
     */
    createPlanningPanel() {
        const container = document.querySelector('.container');
        if (!container) return;

        const planningPanel = document.createElement('div');
        planningPanel.className = 'planning-panel';
        planningPanel.innerHTML = `
            <div class="planning-header">
                <h2>🗺️ 行程规划中</h2>
                <div class="progress-container">
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 0%"></div>
                    </div>
                    <div class="progress-text">准备开始...</div>
                </div>
            </div>
            <div class="planning-content">
                <div class="weather-section" style="display: none;">
                    <h3>🌤️ 天气信息</h3>
                    <div class="weather-info"></div>
                </div>
                <div class="poi-section" style="display: none;">
                    <h3>📍 精选推荐</h3>
                    <div class="poi-results"></div>
                </div>
                <div class="itinerary-section" style="display: none;">
                    <h3>📅 详细行程</h3>
                    <div class="daily-itineraries"></div>
                </div>
                <div class="planning-messages">
                    <div class="message-list"></div>
                </div>
            </div>
        `;

        container.appendChild(planningPanel);
    }

    /**
     * 更新进度
     */
    updateProgress(progress, message) {
        const progressFill = document.querySelector('.progress-fill');
        const progressText = document.querySelector('.progress-text');

        if (progressFill) {
            progressFill.style.width = `${progress}%`;
        }

        if (progressText) {
            progressText.textContent = message || `${progress}%`;
        }
    }

    /**
     * 显示规划消息
     */
    showPlanningMessage(message) {
        const messageList = document.querySelector('.message-list');
        if (!messageList) return;

        const messageElement = document.createElement('div');
        messageElement.className = 'planning-message';
        messageElement.innerHTML = `
            <span class="message-time">${new Date().toLocaleTimeString()}</span>
            <span class="message-content">${message}</span>
        `;

        messageList.appendChild(messageElement);
        messageList.scrollTop = messageList.scrollHeight;
    }

    /**
     * 显示天气信息
     */
    displayWeatherInfo(weather, description) {
        const weatherSection = document.querySelector('.weather-section');
        const weatherInfo = document.querySelector('.weather-info');

        if (weatherSection && weatherInfo) {
            weatherSection.style.display = 'block';
            weatherInfo.innerHTML = `
                <div class="weather-card">
                    <div class="weather-main">${weather.weather}</div>
                    <div class="weather-temp">${weather.temperature}</div>
                    <div class="weather-desc">${description}</div>
                </div>
            `;
        }
    }

    /**
     * 显示POI结果
     */
    displayPOIResults(categoryName, pois, totalCount) {
        const poiSection = document.querySelector('.poi-section');
        const poiResults = document.querySelector('.poi-results');

        if (poiSection && poiResults) {
            poiSection.style.display = 'block';

            const categoryDiv = document.createElement('div');
            categoryDiv.className = 'poi-category';
            categoryDiv.innerHTML = `
                <h4>${categoryName} (${totalCount}个)</h4>
                <div class="poi-list">
                    ${pois.map(poi => this.createPOICard(poi)).join('')}
                </div>
            `;

            poiResults.appendChild(categoryDiv);
        }
    }

    /**
     * 创建POI卡片
     */
    createPOICard(poi) {
        return `
            <div class="poi-card">
                <div class="poi-header">
                    <h5>${poi.name}</h5>
                    <span class="poi-rating">⭐ ${poi.rating}</span>
                </div>
                <div class="poi-address">${poi.address}</div>
                <div class="poi-description">${poi.description}</div>
                <div class="poi-details">
                    <span class="poi-price">${poi.price.value}</span>
                    <span class="poi-hours">${poi.business_hours}</span>
                </div>
                <div class="poi-tags">
                    ${poi.tags.map(tag => `<span class="tag">${tag}</span>`).join('')}
                </div>
            </div>
        `;
    }

    /**
     * 显示每日行程
     */
    displayDailyItinerary(day, itinerary) {
        const itinerarySection = document.querySelector('.itinerary-section');
        const dailyItineraries = document.querySelector('.daily-itineraries');

        if (itinerarySection && dailyItineraries) {
            itinerarySection.style.display = 'block';

            const dayDiv = document.createElement('div');
            dayDiv.className = 'day-itinerary';
            dayDiv.innerHTML = `
                <h4>第${day}天 - ${itinerary.theme}</h4>
                <div class="itinerary-items">
                    ${itinerary.items.map(item => this.createItineraryItem(item)).join('')}
                </div>
                <div class="day-summary">
                    <span>总距离: ${itinerary.total_distance}km</span>
                    <span>预计用时: ${itinerary.estimated_duration}</span>
                </div>
            `;

            dailyItineraries.appendChild(dayDiv);
        }
    }

    /**
     * 创建行程项目
     */
    createItineraryItem(item) {
        const poi = item.poi || {};
        return `
            <div class="itinerary-item">
                <div class="item-time">${item.start_time} - ${item.end_time}</div>
                <div class="item-content">
                    <h6>${poi.name || item.description}</h6>
                    <p>${item.description}</p>
                    <span class="item-duration">${item.duration}</span>
                </div>
            </div>
        `;
    }

    /**
     * 显示规划完成
     */
    showPlanningComplete(result, summary) {
        this.showPlanningMessage('🎉 行程规划完成！');
        
        // 可以在这里添加更多完成后的处理逻辑
        console.log('规划完成:', { result, summary });
    }

    /**
     * 显示错误
     */
    showError(message) {
        this.showPlanningMessage(`❌ ${message}`);
        
        // 可以添加错误处理UI
        const errorDiv = document.createElement('div');
        errorDiv.className = 'error-message';
        errorDiv.textContent = message;
        
        const container = document.querySelector('.planning-content');
        if (container) {
            container.appendChild(errorDiv);
        }
    }

    /**
     * 显示最终行程结果
     */
    displayFinalItinerary(result) {
        console.log('显示最终行程结果:', result);

        // 通知主应用显示行程结果
        if (window.app && result.itinerary) {
            window.app.currentItinerary = result.itinerary;
            window.app.displayItinerary(result.itinerary);
        }
    }

    /**
     * 显示行程摘要
     */
    displayItinerarySummary(summary) {
        console.log('显示行程摘要:', summary);

        // 创建摘要显示
        const summaryHtml = `
            <div class="itinerary-summary">
                <h4>行程摘要</h4>
                <p>目的地: ${summary.destinations?.join(', ') || '未知'}</p>
                <p>天数: ${summary.days || '未知'}</p>
                <p>总预算: ${summary.total_budget || '待估算'}</p>
            </div>
        `;

        // 显示在结果区域
        const resultContainer = document.querySelector('.itinerary-result');
        if (resultContainer) {
            resultContainer.innerHTML = summaryHtml;
        }
    }

    /**
     * 停止规划
     */
    stopPlanning() {
        if (this.planningEventSource) {
            this.planningEventSource.close();
            this.planningEventSource = null;
        }
        this.planningStarted = false;
    }
}

// 创建全局实例
window.planningPhaseManager = new PlanningPhaseManager();

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = PlanningPhaseManager;
}
