/* AutoPilot AI - 重构版界面样式 */

:root {
    --primary-color: #0d6efd;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --info-color: #0dcaf0;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --light-color: #f8f9fa;
    --dark-color: #212529;
    --border-radius: 12px;
    --border-radius-sm: 8px;
    --box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    --box-shadow-lg: 0 8px 24px rgba(0,0,0,0.15);
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

* {
    box-sizing: border-box;
}

body {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 0;
    padding: 0;
    min-height: 100vh;
}

/* 导航栏样式 */
.navbar {
    background: linear-gradient(135deg, var(--primary-color), #0056b3) !important;
    box-shadow: var(--box-shadow);
    border: none;
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.4rem;
    color: white !important;
}

/* 查询输入区域 */
.query-section {
    background: white;
    padding: 2rem 0;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    border-bottom: 1px solid #e9ecef;
}

.query-input-card {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 1.5rem;
    border: 2px solid #e9ecef;
    transition: var(--transition);
}

.query-input-card:focus-within {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.query-input {
    border: none;
    resize: none;
    font-size: 1.1rem;
    padding: 1rem;
    border-radius: var(--border-radius-sm);
    background: #f8f9fa;
    transition: var(--transition);
}

.query-input:focus {
    background: white;
    box-shadow: none;
    border: none;
    outline: none;
}

.query-submit {
    border-radius: var(--border-radius-sm);
    padding: 1rem 1.5rem;
    font-weight: 600;
    white-space: nowrap;
}

/* 主内容区域 */
.main-content {
    min-height: calc(100vh - 200px);
}

/* 面板通用样式 */
.analysis-panel,
.itinerary-panel {
    background: white;
    min-height: calc(100vh - 200px);
    display: flex;
    flex-direction: column;
}

.analysis-panel {
    border-right: 1px solid #e9ecef;
}

.panel-header {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    padding: 1.5rem;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.panel-title {
    margin: 0;
    font-weight: 600;
    color: var(--dark-color);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.panel-controls {
    display: flex;
    gap: 0.5rem;
}

.panel-body {
    flex: 1;
    padding: 1.5rem;
    overflow-y: auto;
}

/* 分析步骤样式 */
.analysis-steps {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.analysis-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 1.5rem;
    background: #f8f9fa;
    border-radius: var(--border-radius);
    border: 2px solid transparent;
    transition: var(--transition);
}

.analysis-item.active {
    background: #e3f2fd;
    border-color: var(--primary-color);
}

.analysis-item.completed {
    background: #e8f5e8;
    border-color: var(--success-color);
}

.analysis-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--primary-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    flex-shrink: 0;
}

.analysis-item.completed .analysis-icon {
    background: var(--success-color);
}

.analysis-content {
    flex: 1;
}

.analysis-title {
    margin: 0 0 0.5rem 0;
    font-weight: 600;
    color: var(--dark-color);
}

.analysis-result {
    color: var(--secondary-color);
    line-height: 1.5;
}

.loading-placeholder {
    color: var(--secondary-color);
    font-style: italic;
}

.analysis-status {
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.analysis-item.completed .analysis-status {
    color: var(--success-color);
    font-size: 1.2rem;
}

/* 用户画像样式 */
.user-profile-content {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.profile-section {
    background: #ffffff;
    border-radius: 8px;
    padding: 1rem;
    border: 1px solid #e9ecef;
}

.profile-section-title {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.75rem;
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--primary-color);
}

.profile-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.profile-tag {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.profile-tag.budget {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.profile-tag.basic-info {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.recommendation-text {
    background: #f8f9fa;
    border-left: 4px solid var(--primary-color);
    padding: 1rem;
    border-radius: 0 8px 8px 0;
    font-style: italic;
    color: #495057;
    line-height: 1.6;
}

/* 控制按钮 */
.analysis-controls {
    padding-top: 1rem;
    border-top: 1px solid #e9ecef;
}

/* 状态视图样式 */
.status-view {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 400px;
    text-align: center;
}

.status-content {
    max-width: 400px;
}

.status-icon {
    font-size: 4rem;
    color: var(--secondary-color);
    margin-bottom: 1rem;
}

.status-spinner {
    margin-bottom: 1.5rem;
}

.status-spinner .spinner-border {
    width: 3rem;
    height: 3rem;
}

.status-title {
    color: var(--dark-color);
    margin-bottom: 1rem;
    font-weight: 600;
}

.status-description {
    color: var(--secondary-color);
    margin: 0;
    line-height: 1.5;
}

/* 行程内容样式 */
.itinerary-content {
    animation: fadeInUp 0.5s ease-out;
}

.itinerary-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #e9ecef;
}

.itinerary-title {
    margin: 0 0 0.5rem 0;
    color: var(--dark-color);
    font-weight: 700;
}

.itinerary-description {
    margin: 0;
    color: var(--secondary-color);
    line-height: 1.5;
}

.itinerary-actions {
    display: flex;
    gap: 0.5rem;
    flex-shrink: 0;
}

/* 统计卡片 */
.itinerary-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: var(--border-radius-sm);
    padding: 1rem;
    text-align: center;
    transition: var(--transition);
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.stat-card:hover {
    box-shadow: var(--box-shadow);
    transform: translateY(-2px);
}

.stat-icon {
    font-size: 1.5rem;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.stat-value {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--dark-color);
    margin: 0;
}

.stat-label {
    font-size: 0.85rem;
    color: var(--secondary-color);
    margin: 0;
}

/* 每日行程 */
.daily-itinerary {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

/* TTS播报指示器 */
.tts-indicator {
    position: fixed;
    top: 80px;
    right: 20px;
    background: var(--primary-color);
    color: white;
    padding: 0.75rem 1rem;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow-lg);
    z-index: 1000;
    animation: slideInRight 0.3s ease-out;
}

.tts-content {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.tts-icon {
    animation: pulse 1.5s infinite;
}

/* 全局加载遮罩 */
.global-loading {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    backdrop-filter: blur(4px);
}

.loading-content {
    background: white;
    padding: 3rem;
    border-radius: var(--border-radius);
    text-align: center;
    box-shadow: var(--box-shadow-lg);
    max-width: 400px;
}

.loading-spinner {
    margin-bottom: 1.5rem;
}

.loading-spinner .spinner-border {
    width: 3rem;
    height: 3rem;
}

.loading-title {
    color: var(--dark-color);
    margin-bottom: 1rem;
    font-weight: 600;
}

.loading-description {
    color: var(--secondary-color);
    margin: 0;
    line-height: 1.5;
}

/* 规划阶段样式 */
.planning-content-wrapper {
    padding: 1rem;
}

.planning-header {
    margin-bottom: 1.5rem;
}

.planning-title {
    color: var(--primary-color);
    font-weight: 600;
    margin-bottom: 1rem;
}

.progress-container {
    margin-bottom: 1rem;
}

.progress-text {
    font-size: 0.9rem;
    color: var(--secondary-color);
    text-align: center;
}

.planning-content-body {
    margin-top: 1rem;
}

.planning-messages {
    background: #f8f9fa;
    border-radius: var(--border-radius-sm);
    padding: 1rem;
    margin-top: 1rem;
}

.message-item {
    display: flex;
    align-items: center;
    padding: 0.5rem 0;
    color: var(--dark-color);
}

.message-item i {
    margin-right: 0.5rem;
}

/* 天气卡片样式 */
.weather-info {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    margin-bottom: 1rem;
}

.weather-day {
    flex: 1;
    min-width: 120px;
    text-align: center;
    padding: 1rem;
    background: linear-gradient(135deg, #74b9ff, #0984e3);
    color: white;
    border-radius: var(--border-radius-sm);
    box-shadow: 0 2px 8px rgba(116, 185, 255, 0.3);
}

.weather-day .date {
    font-weight: 600;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.weather-day .temp {
    font-size: 1.2rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
}

.weather-day .desc {
    font-size: 0.8rem;
    opacity: 0.9;
}

.weather-section {
    margin-bottom: 1.5rem;
}

.weather-section h6 {
    color: var(--primary-color);
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

/* POI结果样式 */
.poi-results {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 1rem;
}

.poi-item {
    background: white;
    border-radius: var(--border-radius-sm);
    padding: 1rem;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transition: var(--transition);
}

.poi-item:hover {
    transform: translateY(-2px);
    box-shadow: var(--box-shadow);
}

.poi-item .poi-name {
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.poi-item .poi-address {
    font-size: 0.9rem;
    color: var(--secondary-color);
    margin-bottom: 0.5rem;
}

.poi-item .poi-rating {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.poi-item .poi-rating .stars {
    color: #ffc107;
}

/* 每日行程样式 */
.daily-itineraries {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.daily-itinerary {
    background: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    box-shadow: var(--box-shadow);
}

.daily-itinerary .day-header {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid var(--light-color);
}

.daily-itinerary .day-number {
    background: var(--primary-color);
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    margin-right: 1rem;
}

.daily-itinerary .day-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--dark-color);
    margin: 0;
}

.daily-itinerary .day-activities {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.activity-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: var(--border-radius-sm);
    margin-bottom: 1rem;
}

.activity-image {
    flex-shrink: 0;
    width: 120px;
    height: 80px;
}

.image-placeholder {
    width: 100%;
    height: 100%;
    background: #e9ecef;
    border-radius: var(--border-radius-sm);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: var(--secondary-color);
    font-size: 0.8rem;
    text-align: center;
}

.poi-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: var(--border-radius-sm);
}

.poi-info {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 0.5rem;
    background: linear-gradient(135deg, #e3f2fd, #bbdefb);
    border-radius: var(--border-radius-sm);
}

.poi-details {
    text-align: center;
    margin-top: 0.25rem;
}

.poi-name {
    font-size: 0.7rem;
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 0.25rem;
}

.poi-address {
    font-size: 0.6rem;
    color: var(--secondary-color);
    margin-bottom: 0.25rem;
}

.poi-rating {
    font-size: 0.6rem;
    color: #ffc107;
}

/* 行程卡片容器样式 */
.itinerary-cards-container {
    padding: 1rem;
}

.itinerary-header {
    text-align: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid var(--light-color);
}

.itinerary-title {
    color: var(--primary-color);
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.itinerary-description {
    color: var(--secondary-color);
    font-size: 1.1rem;
    margin: 0;
}

.itinerary-actions {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-top: 2rem;
    padding-top: 1rem;
    border-top: 1px solid var(--light-color);
}

.day-dining {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: var(--border-radius-sm);
    padding: 1rem;
    margin-top: 1rem;
}

.day-dining h6 {
    color: #856404;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.day-dining p {
    margin: 0.25rem 0;
    color: #856404;
    font-size: 0.9rem;
}

.activity-transport {
    font-size: 0.8rem;
    color: var(--info-color);
    margin-top: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.activity-item .activity-time {
    background: var(--info-color);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: 600;
    white-space: nowrap;
}

.activity-item .activity-content {
    flex: 1;
}

.activity-item .activity-name {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 0.25rem;
}

.activity-item .activity-desc {
    font-size: 0.9rem;
    color: var(--secondary-color);
}

/* 响应式设计增强 */
@media (max-width: 768px) {
    .poi-results {
        grid-template-columns: 1fr;
    }

    .weather-info {
        flex-direction: column;
    }

    .daily-itinerary .day-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .activity-item {
        flex-direction: column;
        gap: 0.5rem;
    }
}
