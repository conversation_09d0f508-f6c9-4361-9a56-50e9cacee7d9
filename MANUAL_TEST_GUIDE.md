# 手动测试指南

## 🎯 测试目标
验证旅行规划应用的完整功能，确保从分析到规划的整个流程正常工作。

## 🚀 启动应用

1. **启动后端服务**
   ```bash
   python start_server.py --reload
   ```
   
2. **访问前端页面**
   ```
   http://localhost:8000/static/index.html
   ```

## 📋 测试步骤

### 第一阶段：分析阶段测试

1. **输入测试查询**
   - 在查询输入框中输入：`上海3天亲子慢节奏趣味性儿童友好`
   - 用户ID保持默认：`1`

2. **开始分析**
   - 点击"开始规划"按钮
   - 观察左侧分析面板的变化

3. **验证分析步骤**
   - ✅ 解析用户需求和画像：应显示"上海|3天 | 亲子 | 亲子游"等信息
   - ✅ 景点偏好类型：应显示"景点推荐：乐园/休闲"等信息
   - ✅ 美食偏好：应显示口味偏好信息
   - ✅ 住宿偏好：应显示"连锁1800-1500元、安静整洁、免费停车"

4. **检查分析完成**
   - 所有分析项应显示绿色勾号
   - "立即规划"按钮应该出现并可点击

### 第二阶段：规划阶段测试

1. **启动规划**
   - 点击"立即规划"按钮
   - 按钮应变为"规划中..."状态

2. **观察规划过程**
   - 右侧面板应显示"正在生成旅行方案..."
   - 进度应逐步更新

3. **验证规划完成**
   - 应收到"旅行规划已完成！"的提示
   - 整个流程应在2分钟内完成

## 🔧 后端API测试

### 分析阶段API测试
```bash
python test_analysis_phase.py
```

### 规划阶段API测试
```bash
python test_planning_phase.py
```

### 完整工作流程测试
```bash
python test_complete_workflow.py
```

## ✅ 预期结果

### 分析阶段
- 4个分析步骤全部完成
- 每个步骤显示具体的分析内容
- "立即规划"按钮正确显示

### 规划阶段
- 成功生成3天行程
- 显示规划完成消息
- 整个流程无错误

### 后端测试
- 所有API测试通过
- SSE事件流正常工作
- 数据库连接正常

## 🐛 常见问题

### 1. 分析步骤卡住
- 检查后端日志是否有错误
- 确认数据库连接正常
- 重新启动后端服务

### 2. 立即规划按钮不显示
- 确认所有4个分析步骤都已完成
- 检查浏览器控制台是否有JavaScript错误
- 刷新页面重试

### 3. 规划阶段超时
- 检查网络连接
- 确认LLM API配置正确
- 查看后端日志获取详细错误信息

## 📊 测试报告模板

```
测试时间：____年__月__日 __:__
测试人员：__________

分析阶段：
□ 用户需求解析 - 通过/失败
□ 景点偏好分析 - 通过/失败  
□ 美食偏好分析 - 通过/失败
□ 住宿偏好分析 - 通过/失败

规划阶段：
□ 行程生成 - 通过/失败
□ 规划完成 - 通过/失败

整体评价：
□ 功能完整性 - 优秀/良好/需改进
□ 用户体验 - 优秀/良好/需改进
□ 性能表现 - 优秀/良好/需改进

备注：
_________________________________
```

## 🎉 成功标准

应用被认为"完整可用"需要满足：

1. ✅ 分析阶段4个步骤全部正常工作
2. ✅ 规划阶段能够生成完整行程
3. ✅ 前端界面交互流畅
4. ✅ 后端API响应正常
5. ✅ 整个流程在合理时间内完成（<2分钟）
6. ✅ 无严重错误或崩溃
