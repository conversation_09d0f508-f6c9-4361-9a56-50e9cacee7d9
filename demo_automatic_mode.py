"""
TravelPlannerAgent LangGraph 全自动模式演示

演示重构后的LangGraph实现的全自动规划功能。
专注于全自动模式，交互模式预留钩子。
"""

import asyncio
import json
import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 模拟导入（实际使用时需要确保依赖正确安装）
try:
    from src.agents.travel_planner_agent_langgraph import TravelPlannerAgentLangGraph
except ImportError:
    print("注意：实际运行需要安装相关依赖和配置数据库连接")
    print("这里仅演示重构后的代码结构和接口")
    
    # 创建模拟类用于演示
    class TravelPlannerAgentLangGraph:
        def __init__(self, enable_interaction_hooks=False):
            self.enable_interaction_hooks = enable_interaction_hooks
            print(f"✓ TravelPlannerAgent初始化完成 - 交互钩子: {'启用' if enable_interaction_hooks else '禁用'}")
        
        async def plan_travel_automatic(self, user_id, query, **kwargs):
            print(f"✓ 开始全自动规划 - 用户: {user_id}")
            print(f"✓ 查询: {query}")
            await asyncio.sleep(1)  # 模拟处理时间
            return {
                "session_id": f"auto_plan_{user_id}_{int(datetime.now().timestamp())}",
                "status": "completed",
                "planning_mode": "automatic",
                "core_intent": {"destinations": ["北京"], "days": 3},
                "daily_itineraries": [{"day": 1}, {"day": 2}, {"day": 3}]
            }
        
        async def plan_travel_stream_automatic(self, user_id, query, **kwargs):
            events = [
                "event: stream_start\ndata: {\"message\": \"开始全自动规划\"}\n\n",
                "event: stage_progress\ndata: {\"stage\": \"intent_analysis\", \"progress\": 20}\n\n",
                "event: stage_progress\ndata: {\"stage\": \"preference_analysis\", \"progress\": 60}\n\n",
                "event: stage_progress\ndata: {\"stage\": \"itinerary_generation\", \"progress\": 90}\n\n",
                "event: planning_completed\ndata: {\"message\": \"规划完成\", \"progress\": 100}\n\n",
                "event: stream_end\ndata: {\"message\": \"处理完成\"}\n\n"
            ]
            for event in events:
                await asyncio.sleep(0.5)
                yield event
        
        def get_automatic_mode_info(self):
            return {
                "mode": "automatic",
                "description": "完全自动化的旅行规划模式",
                "features": ["无需用户交互", "完整的规划流程", "双模运行支持"],
                "interaction_hooks_enabled": self.enable_interaction_hooks
            }
        
        def get_graph_visualization(self, include_interaction_hooks=False):
            if include_interaction_hooks:
                return "graph TD\n    A[核心意图分析] --> UC[用户确认钩子] --> B[偏好分析]"
            else:
                return "graph TD\n    A[核心意图分析] --> B[偏好分析] --> C[行程生成] --> D[完成]"


async def demo_automatic_planning():
    """演示全自动规划功能"""
    print("=" * 60)
    print("演示1: 全自动旅行规划功能")
    print("=" * 60)
    
    # 创建全自动模式的Agent
    agent = TravelPlannerAgentLangGraph(enable_interaction_hooks=False)
    
    # 示例用户画像
    user_profile = {
        "user_id": "demo_user_001",
        "age_group": "25-35",
        "travel_style": "深度游",
        "interests": ["文化历史", "美食"]
    }
    
    query = "我想去北京玩3天，主要想看故宫、长城这些历史文化景点"
    
    print(f"用户查询: {query}")
    print("\n开始全自动规划...")
    
    try:
        start_time = datetime.now()
        result = await agent.plan_travel_automatic(
            user_id="demo_user_001",
            query=query,
            user_profile=user_profile
        )
        end_time = datetime.now()
        
        print(f"\n✓ 全自动规划完成! 耗时: {(end_time - start_time).total_seconds():.2f}秒")
        print(f"✓ 状态: {result.get('status')}")
        print(f"✓ 规划模式: {result.get('planning_mode')}")
        print(f"✓ 会话ID: {result.get('session_id')}")
        
        if result.get('core_intent'):
            intent = result['core_intent']
            print(f"✓ 目的地: {intent.get('destinations')}")
            print(f"✓ 天数: {intent.get('days')}")
        
        if result.get('daily_itineraries'):
            print(f"✓ 生成了 {len(result['daily_itineraries'])} 天的详细行程")
        
    except Exception as e:
        print(f"✗ 演示失败: {str(e)}")


async def demo_stream_planning():
    """演示流式规划功能"""
    print("\n" + "=" * 60)
    print("演示2: 全自动流式规划功能")
    print("=" * 60)
    
    agent = TravelPlannerAgentLangGraph(enable_interaction_hooks=False)
    
    query = "我想去上海玩2天，主要想看外滩和迪士尼"
    
    print(f"用户查询: {query}")
    print("\n开始流式规划...")
    print("-" * 40)
    
    try:
        event_count = 0
        async for sse_event in agent.plan_travel_stream_automatic(
            user_id="demo_user_002",
            query=query
        ):
            event_count += 1
            
            # 解析并显示事件
            lines = sse_event.strip().split('\n')
            event_type = None
            event_data = None
            
            for line in lines:
                if line.startswith('event:'):
                    event_type = line[6:].strip()
                elif line.startswith('data:'):
                    try:
                        event_data = json.loads(line[5:].strip())
                    except:
                        event_data = line[5:].strip()
            
            if event_type:
                print(f"[{event_type}] ", end="")
                if isinstance(event_data, dict):
                    if 'message' in event_data:
                        print(event_data['message'])
                    elif 'progress' in event_data:
                        print(f"进度: {event_data['progress']}%")
                    else:
                        print(f"数据: {event_data}")
                else:
                    print(event_data)
        
        print("-" * 40)
        print(f"✓ 流式规划完成，共接收 {event_count} 个事件")
        
    except Exception as e:
        print(f"✗ 流式演示失败: {str(e)}")


def demo_interaction_hooks():
    """演示交互钩子预留功能"""
    print("\n" + "=" * 60)
    print("演示3: 交互模式钩子预留功能")
    print("=" * 60)
    
    # 创建启用交互钩子的Agent
    agent_with_hooks = TravelPlannerAgentLangGraph(enable_interaction_hooks=True)
    agent_without_hooks = TravelPlannerAgentLangGraph(enable_interaction_hooks=False)
    
    print("✓ 全自动模式信息:")
    auto_info = agent_without_hooks.get_automatic_mode_info()
    print(f"  模式: {auto_info['mode']}")
    print(f"  描述: {auto_info['description']}")
    print(f"  交互钩子: {'启用' if auto_info['interaction_hooks_enabled'] else '禁用'}")
    
    print("\n✓ 交互钩子启用模式信息:")
    hook_info = agent_with_hooks.get_automatic_mode_info()
    print(f"  交互钩子: {'启用' if hook_info['interaction_hooks_enabled'] else '禁用'}")
    
    print("\n✓ 工作流图可视化:")
    print("  全自动模式图:")
    auto_graph = agent_without_hooks.get_graph_visualization(include_interaction_hooks=False)
    print(f"    {auto_graph}")
    
    print("\n  包含交互钩子的图:")
    hook_graph = agent_with_hooks.get_graph_visualization(include_interaction_hooks=True)
    print(f"    {hook_graph}")


async def demo_backward_compatibility():
    """演示向后兼容性"""
    print("\n" + "=" * 60)
    print("演示4: 向后兼容性")
    print("=" * 60)
    
    agent = TravelPlannerAgentLangGraph()
    
    print("✓ 测试向后兼容的方法调用:")
    
    # 使用原有的方法名（内部调用全自动模式）
    try:
        result = await agent.plan_travel(
            user_id="compat_user",
            query="我想去杭州玩1天"
        )
        print(f"  plan_travel() -> 状态: {result.get('status')}")
    except Exception as e:
        print(f"  plan_travel() -> 错误: {str(e)}")
    
    print("\n✓ 新的明确方法调用:")
    try:
        result = await agent.plan_travel_automatic(
            user_id="auto_user", 
            query="我想去杭州玩1天"
        )
        print(f"  plan_travel_automatic() -> 状态: {result.get('status')}")
    except Exception as e:
        print(f"  plan_travel_automatic() -> 错误: {str(e)}")


async def main():
    """主演示函数"""
    print("TravelPlannerAgent LangGraph 全自动模式演示")
    print("=" * 60)
    print("本演示展示重构后的LangGraph实现的全自动规划功能")
    print("专注于全自动模式，交互模式预留钩子接口")
    print("=" * 60)
    
    try:
        # 运行各个演示
        await demo_automatic_planning()
        await demo_stream_planning()
        demo_interaction_hooks()
        await demo_backward_compatibility()
        
        print("\n" + "=" * 60)
        print("✓ 所有演示完成！")
        print("✓ 重构后的LangGraph实现已准备就绪")
        print("✓ 全自动模式：完全自动化，无需用户交互")
        print("✓ 交互模式：预留钩子，可在后续版本实现")
        print("✓ 向后兼容：保持原有接口，内部使用全自动模式")
        print("=" * 60)
        
    except KeyboardInterrupt:
        print("\n演示被用户中断")
    except Exception as e:
        print(f"\n演示过程中发生错误: {str(e)}")


if __name__ == "__main__":
    # 运行演示
    asyncio.run(main())
