from minio import <PERSON>o
from minio.error import S3<PERSON>rror
from typing import Optional
import os
from datetime import timedelta
from src.core.config import get_settings
from io import BytesIO

class MinioClient:
    def __init__(self):
        settings = get_settings()
        minio_conf = settings.minio
        self.client = Minio(
            endpoint=minio_conf.endpoint,
            access_key=minio_conf.access_key,
            secret_key=minio_conf.secret_key,
            secure=minio_conf.secure
        )
        self.bucket_name = minio_conf.bucket
        self._ensure_bucket_exists()

    def _ensure_bucket_exists(self):
        """Ensure the bucket exists, create if it doesn't"""
        try:
            if not self.client.bucket_exists(self.bucket_name):
                self.client.make_bucket(self.bucket_name)
        except S3Error as e:
            raise Exception(f"Failed to create/check bucket: {str(e)}")

    async def upload_file(self, file_path: str, object_name: Optional[str] = None) -> str:
        """
        Upload a file to MinIO and return its URL
        
        Args:
            file_path: Local path to the file
            object_name: Name to use in MinIO (if None, use basename of file_path)
            
        Returns:
            str: URL to access the uploaded file
        """
        try:
            if not object_name:
                object_name = os.path.basename(file_path)

            # Upload the file
            self.client.fput_object(
                self.bucket_name,
                object_name,
                file_path
            )

            # Generate a presigned URL that's valid for 7 days
            url = self.client.presigned_get_object(
                self.bucket_name,
                object_name,
                expires=timedelta(days=7)
            )

            return url

        except S3Error as e:
            raise Exception(f"Failed to upload file to MinIO: {str(e)}")

    async def upload_file_content(self, content: bytes, object_name: str, content_type: str = "application/octet-stream") -> str:
        """
        Upload file content directly to MinIO and return its URL
        
        Args:
            content: The file content in bytes
            object_name: Name to use in MinIO
            content_type: The content type of the file
            
        Returns:
            str: URL to access the uploaded file
        """
        try:
            # Create BytesIO object from content
            content_stream = BytesIO(content)
            content_size = len(content)

            # Upload the content
            self.client.put_object(
                self.bucket_name,
                object_name,
                content_stream,
                content_size,
                content_type=content_type
            )

            # Generate a presigned URL that's valid for 7 days
            url = self.client.presigned_get_object(
                self.bucket_name,
                object_name,
                expires=timedelta(days=7)
            )

            return url

        except S3Error as e:
            raise Exception(f"Failed to upload content to MinIO: {str(e)}")

    async def delete_file(self, object_name: str):
        """Delete a file from MinIO"""
        try:
            self.client.remove_object(self.bucket_name, object_name)
        except S3Error as e:
            raise Exception(f"Failed to delete file from MinIO: {str(e)}")

    def sync_upload_file(self, file_path: str, object_name: Optional[str] = None) -> str:
        """
        同步上传文件到 MinIO 并返回其 URL。
        Args:
            file_path: 本地文件路径
            object_name: MinIO中的对象名（可选）
        Returns:
            str: 可访问的 presigned URL
        """
        try:
            if not object_name:
                object_name = os.path.basename(file_path)
            self.client.fput_object(
                self.bucket_name,
                object_name,
                file_path
            )
            url = self.client.presigned_get_object(
                self.bucket_name,
                object_name,
                expires=timedelta(days=7)
            )
            return url
        except S3Error as e:
            raise Exception(f"Failed to upload file to MinIO: {str(e)}") 