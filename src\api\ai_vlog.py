from fastapi import APIRouter, UploadFile, File, Form, HTTPException, Path, Request
from fastapi.responses import J<PERSON><PERSON>esponse
from typing import List, Optional
import httpx
import os
import tempfile
import traceback
from src.database.minio_client import MinioClient
import logging
import json
import aiofiles
import uuid
from src.tools.comfyui import logger, load_workflow, upload_image, send_prompt, wait_for_image

router = APIRouter(prefix="/api/ai_vlog", tags=["旅行规划"])
logger = logging.getLogger("ai_vlog")
minio_client = MinioClient()

MONEY_PRINTER_API = "http://************:8195/api/v1/videos"
server_name = ""
async def download_and_upload_to_minio(image_url: str, object_prefix: str = "image") -> str:
    """下载图片并上传到minio，返回minio url"""
    async with httpx.AsyncClient() as client:
        resp = await client.get(image_url)
        resp.raise_for_status()
        content = resp.content
        # 获取扩展名 获取有问题，先写png
        ext = image_url.split('.')[-1].split('?')[0]
        object_name = f"{object_prefix}/{uuid.uuid4()}.png"
        minio_url = await minio_client.upload_file_content(content, object_name, content_type="image/jpeg")
        return minio_url

def build_image_to_image_workflow(prompt: str, task_id: str) -> dict:
    """构建图生图工作流"""
    try:
        # 加载工作流
        workflow = load_workflow("magic_camera.json")

        # 设置提示词
        if "17" not in workflow:
            logger.error("工作流中找不到节点17")
            raise HTTPException(status_code=500, detail="Node 17 not found in workflow")

        workflow["17"]["inputs"]["image"] = prompt
        logger.info(f"设置图片: {prompt}")

        # 设置保存路径
        if "63" not in workflow:
            logger.error("工作流中找不到节点63")
            raise HTTPException(status_code=500, detail="Node 63 not found in workflow")

        return workflow, "63"
    except Exception as e:
        logger.error(f"构建工作流时出错: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"Error building workflow: {str(e)}")

# 定义接口
async def image_to_image_comfyui(file: UploadFile):
    try:
        logger.info(f"\n========== 开始转换图片 ==========")
        logger.info(f"文件名: {file.filename}")
        # 上传文件到远程服务器
        uploaded_filename = await upload_image(server_name, file.file, "待转换图片")
        logger.info(f"成功上传文件，获得文件名称: {uploaded_filename}")

        # 构建工作流
        workflow, output_node_id = build_image_to_image_workflow(
            uploaded_filename,
            str(uuid.uuid4())
        )
        logger.info(f"\n当前工作流配置: {json.dumps(workflow, indent=2)}")

        # 发送生成请求
        data = await send_prompt(server_name, workflow)
        logger.info(f"ComfyUI API 响应: {data}")

        # 等待图片生成
        image_url = await wait_for_image(server_name, data["prompt_id"], output_node_id, "")
        logger.info(f"生成的图片URL: {image_url}")

        # 新增：下载并上传到minio
        minio_url = await download_and_upload_to_minio(image_url)
        return minio_url
    except HTTPException as e:
        logger.error(f"HTTP异常: {str(e)}")
        raise e
    except Exception as e:
        logger.error(f"调用image_to_image接口出错: {str(e)}")
        raise HTTPException(status_code=500, detail="内部服务器错误")

async def image_to_image(files: List[UploadFile]) -> List[str]:
    """Save multiple files and return their URLs"""
    if not files:
        return []
    urls = []
    for file in files:
        # 过滤掉空文件或无效文件
        if not file.filename or file.size == 0:
            print(f'Skipping empty file: {file.filename}, size: {file.size}')
            continue
        url = await image_to_image_comfyui(file)
        urls.append(url)
    print(urls)
    return urls
    
async def save_file_to_minio(file: UploadFile, file_type: str) -> str:
    """
    Save an uploaded file to MinIO and return its URL
    
    Args:
        file: The uploaded file
        file_type: Type of file (image/audio/video)
        
    Returns:
        str: The URL to access the file
    """
    try:
        # Read uploaded file content
        content = await file.read()
        
        # Generate unique object name
        suffix = os.path.splitext(file.filename)[1]
        object_name = f"{file_type}/{str(uuid.uuid4())}{suffix}"
        
        # Upload to MinIO and get URL
        url = await minio_client.upload_file_content(
            content,
            object_name,
            content_type=file.content_type or "application/octet-stream"
        )
        
        return url
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to upload file: {str(e)}")

async def save_files(files: List[UploadFile], file_type: str) -> List[str]:
    """Save multiple files and return their URLs"""
    if not files:
        return []
    urls = []
    for file in files:
        # 过滤掉空文件或无效文件
        if not file.filename or file.size == 0:
            print(f'Skipping empty file: {file.filename}, size: {file.size}')
            continue
        url = await save_file_to_minio(file, file_type)
        urls.append(url)
    return urls

@router.post("/generate")
async def generate_ai_vlog(
    topic: str = Form(..., description="视频主题"),
    script: str = Form(..., description="视频文案"),
    magic_camera: bool = Form(False, description="是否使用魔法相机（图生图）"),
    images: Optional[List[UploadFile]] = File(None, description="图片文件列表"),
    audios: Optional[List[UploadFile]] = File(None, description="音频文件列表"),
    videos: Optional[List[UploadFile]] = File(None, description="视频文件列表")
):
    try:
        # Save all media files to MinIO and get their URLs
        if magic_camera and images:
            image_urls = await image_to_image(images)
        else:
            image_urls = await save_files(images, "image")
        audio_urls = await save_files(audios, "audio")
        video_urls = await save_files(videos, "video")
        data = {
            "video_subject": topic,
            "video_script": script,
            # "video_terms": "",  # 可扩展为前端输入
            "video_aspect": "16:9",
            "video_concat_mode": "random",
            "video_transition_mode": "None",
            "video_clip_duration": 2,
            "video_count": 1,
            "video_source": "local",
            "video_materials": [
                {
                    "provider": "minio",#"minio","local",
                    "url": url,
                    # "url": "./storage/local_videos/1750fb81-e22c-4a0e-be67-c098d4236573_微信图片_20250625101558.jpg",
                    # "url": "http://************:8202/view?filename=d3ae7b38_00001_.png&subfolder=&type=output",
                    "duration": 0
                } for url in (image_urls + video_urls)
            ],
            "video_language": "",
            "voice_name": "zh-CN-liaoning-XiaobeiNeural-Female",
            "voice_volume": 1.0,
            "voice_rate": 1.0,
            "bgm_type": "minio" if audio_urls else "random",
            "bgm_file": audio_urls[0] if audio_urls else "",
            "bgm_volume": 0.2,
            "subtitle_enabled": True,
            "subtitle_position": "bottom",
            "custom_position": 70.0,
            "font_name": "MicrosoftYaHeiBold.ttc",
            "text_fore_color": "#FFFFFF",
            "text_background_color": True,
            "font_size": 60,
            "stroke_color": "#000000",
            "stroke_width": 1.5,
            "n_threads": 2,
            "paragraph_number": 1
        }
        print("MONEY_PRINTER_API ", MONEY_PRINTER_API)
        print("post data ", json.dumps(data, ensure_ascii=False, indent=2))
        async with httpx.AsyncClient(timeout=600) as client:
            response = await client.post(MONEY_PRINTER_API, json=data)
            if response.status_code != 200:
                logger.error(f"视频生成失败: {response.text}")
                raise HTTPException(status_code=500, detail=f"视频生成失败: {response.text}")
            
            try:
                result = response.json()
                task_id = result.get("task_id") or result.get("data", {}).get("task_id")
                if task_id:
                    return JSONResponse({"code": 200, "task_id": task_id})
                else:
                    logger.error(f"创建任务失败: {result}")
                    return JSONResponse({"code": 500, "detail": "创建任务失败", "raw": result}, status_code=500)
            except Exception as e:
                logger.error(f"解析视频生成API返回失败: {e}\n{traceback.format_exc()}")
                return JSONResponse({"code": 500, "detail": "视频生成API返回非JSON格式", "raw": response.text}, status_code=500)

    except Exception as e:
        logger.error(f"AI Vlog生成接口异常: {e}\n{traceback.format_exc()}")
        return JSONResponse({"code": 500, "detail": str(e), "trace": traceback.format_exc()}, status_code=500)

@router.get("/task_status/{task_id}", summary="查询AI视频任务状态")
async def get_ai_vlog_task_status(task_id: str = Path(..., description="任务ID")):
    """查询MoneyPrinterTurbo生成视频任务的状态和结果"""
    try:
        # 假设MoneyPrinterTurbo的API为 http://host:port/tasks/{task_id}
        url = f"{MONEY_PRINTER_API.rsplit('/videos', 1)[0]}/tasks/{task_id}"
        async with httpx.AsyncClient(timeout=30) as client:
            resp = await client.get(url)
        if resp.status_code != 200:
            return JSONResponse({"code": resp.status_code, "detail": "查询失败", "data": resp.text}, status_code=500)
        result = resp.json()
        return JSONResponse({"code": 200, "data": result.get("data", {})})
    except Exception as e:
        logger.error(f"查询AI视频任务状态异常: {e}\n{traceback.format_exc()}")
        return JSONResponse({"code": 500, "detail": str(e), "raw": traceback.format_exc()}, status_code=500)