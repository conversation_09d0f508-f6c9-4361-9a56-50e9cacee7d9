["tests/test_travel_planner_langgraph.py::TestTravelPlannerLangGraph::test_stream_planning", "tests/unit/core/test_cache_manager.py::TestCacheManager::test_cache_clear_pattern", "tests/unit/core/test_cache_manager.py::TestCacheManager::test_cache_delete", "tests/unit/core/test_cache_manager.py::TestCacheManager::test_cache_delete_not_found", "tests/unit/core/test_cache_manager.py::TestCacheManager::test_cache_hit", "tests/unit/core/test_cache_manager.py::TestCacheManager::test_cache_key_consistency", "tests/unit/core/test_cache_manager.py::TestCacheManager::test_cache_key_generation", "tests/unit/core/test_cache_manager.py::TestCacheManager::test_cache_manager_close", "tests/unit/core/test_cache_manager.py::TestCacheManager::test_cache_miss", "tests/unit/core/test_cache_manager.py::TestCacheManager::test_cache_write", "tests/unit/core/test_cache_manager.py::TestCacheManager::test_cache_write_with_custom_ttl", "tests/unit/core/test_cache_manager.py::TestCacheManager::test_corrupted_cache_data", "tests/unit/core/test_cache_manager.py::TestCacheManager::test_error_handling_redis_failure", "tests/unit/core/test_cache_manager.py::TestCacheManager::test_stats_functionality", "tests/unit/core/test_cache_manager.py::TestCacheManager::test_ttl_configuration", "tests/unit/core/test_cache_manager.py::TestCachedToolCallDecorator::test_decorator_cache_hit", "tests/unit/core/test_cache_manager.py::TestCachedToolCallDecorator::test_decorator_cache_miss", "tests/unit/core/test_cache_manager.py::TestCachedToolCallDecorator::test_decorator_error_handling", "tests/unit/core/test_cache_manager.py::TestGlobalCacheManager::test_global_cache_manager_singleton"]