"""
数据库驱动的用户画像服务

基于现有的MySQL数据库架构，提供真实的用户画像查询功能
"""

import logging
from typing import Dict, Any, List, Optional
from datetime import datetime

from src.database.mysql_client import fetch_one, fetch_all

logger = logging.getLogger(__name__)


class UserProfileDatabaseService:
    """数据库驱动的用户画像服务"""
    
    def __init__(self):
        self.logger = logger
    
    async def get_user_comprehensive_profile(self, user_id: int) -> Dict[str, Any]:
        """
        获取用户的综合画像数据

        Args:
            user_id: 用户ID

        Returns:
            包含所有用户画像信息的综合数据
        """
        self.logger.info(f"开始获取用户 {user_id} 的综合画像数据")

        comprehensive_profile = {
            "user_id": user_id,
            "user_summary": None,
            "user_memories": [],
            "travel_profile": None,
            "travel_history": [],
            "profile_completeness": 0.0,
            "analysis_context": {}
        }

        try:
            # 1. 获取用户画像摘要（从dh_user_profile数据库）
            summary_query = """
            SELECT user_id, summary, keywords, model_version, updated_at
            FROM dh_user_profile.user_summaries
            WHERE user_id = %s
            """
            user_summary = await fetch_one(summary_query, (user_id,))

            if user_summary:
                comprehensive_profile["user_summary"] = {
                    "summary": user_summary["summary"],
                    "keywords": user_summary["keywords"],
                    "model_version": user_summary["model_version"],
                    "updated_at": user_summary["updated_at"].isoformat() if user_summary["updated_at"] else None
                }
                self.logger.info(f"用户 {user_id} 画像摘要: {user_summary['summary'][:100]}...")

            # 2. 获取用户记忆（最近10条高置信度记忆）
            memories_query = """
            SELECT id, user_id, memory_content, source_session_id,
                   confidence, created_at, last_accessed
            FROM dh_user_profile.user_memories
            WHERE user_id = %s AND confidence >= 0.7
            ORDER BY created_at DESC
            LIMIT 10
            """
            user_memories = await fetch_all(memories_query, (user_id,))

            if user_memories:
                comprehensive_profile["user_memories"] = [
                    {
                        "id": memory["id"],
                        "content": memory["memory_content"],
                        "confidence": memory["confidence"],
                        "created_at": memory["created_at"].isoformat() if memory["created_at"] else None,
                        "source_session_id": memory["source_session_id"]
                    }
                    for memory in user_memories
                ]
                self.logger.info(f"用户 {user_id} 获取到 {len(user_memories)} 条高置信度记忆")

            # 3. 获取旅行历史（最近5次行程）
            history_query = """
            SELECT id, user_id, title, city_name, total_days, start_date,
                   total_distance, notes, created_at
            FROM dh_tripplanner.itineraries
            WHERE user_id = %s
            ORDER BY created_at DESC
            LIMIT 5
            """
            travel_history = await fetch_all(history_query, (user_id,))

            if travel_history:
                comprehensive_profile["travel_history"] = [
                    {
                        "id": itinerary["id"],
                        "title": itinerary["title"],
                        "city_name": itinerary["city_name"],
                        "total_days": itinerary["total_days"],
                        "start_date": itinerary["start_date"].isoformat() if itinerary["start_date"] else None,
                        "notes": itinerary["notes"],
                        "created_at": itinerary["created_at"].isoformat() if itinerary["created_at"] else None
                    }
                    for itinerary in travel_history
                ]
                self.logger.info(f"用户 {user_id} 获取到 {len(travel_history)} 条旅行历史")

            # 4. 计算画像完整度
            completeness_score = 0.0
            if comprehensive_profile["user_summary"]:
                completeness_score += 0.4
            if comprehensive_profile["user_memories"]:
                completeness_score += 0.3
            if comprehensive_profile["travel_history"]:
                completeness_score += 0.3

            comprehensive_profile["profile_completeness"] = completeness_score

            # 5. 构建分析上下文
            comprehensive_profile["analysis_context"] = self._build_analysis_context(
                comprehensive_profile
            )

            self.logger.info(f"用户 {user_id} 综合画像获取完成，完整度: {completeness_score:.2f}")

        except Exception as e:
            self.logger.error(f"获取用户 {user_id} 综合画像失败: {str(e)}")
            # 返回基础结构，避免完全失败

        return comprehensive_profile
    
    def _build_analysis_context(self, profile: Dict[str, Any]) -> Dict[str, Any]:
        """
        基于用户画像构建分析上下文
        
        Args:
            profile: 用户综合画像数据
            
        Returns:
            分析上下文数据
        """
        context = {
            "has_summary": bool(profile.get("user_summary")),
            "has_memories": bool(profile.get("user_memories")),
            "has_travel_history": bool(profile.get("travel_history")),
            "memory_count": len(profile.get("user_memories", [])),
            "travel_count": len(profile.get("travel_history", [])),
            "keywords": [],
            "recent_destinations": [],
            "travel_patterns": {}
        }
        
        # 提取关键词
        if profile.get("user_summary") and profile["user_summary"].get("keywords"):
            context["keywords"] = profile["user_summary"]["keywords"]
        
        # 提取最近目的地
        if profile.get("travel_history"):
            context["recent_destinations"] = [
                travel["city_name"] for travel in profile["travel_history"][:3]
            ]
        
        # 分析旅行模式
        if profile.get("travel_history"):
            total_days = sum(travel["total_days"] for travel in profile["travel_history"])
            avg_days = total_days / len(profile["travel_history"]) if profile["travel_history"] else 0
            
            context["travel_patterns"] = {
                "average_trip_days": round(avg_days, 1),
                "total_trips": len(profile["travel_history"]),
                "preferred_destinations": list(set(context["recent_destinations"]))
            }
        
        return context
    
    async def get_user_travel_preferences(self, user_id: int) -> Dict[str, Any]:
        """
        获取用户旅行偏好（从数据库中的旅行画像表）

        Args:
            user_id: 用户ID

        Returns:
            用户旅行偏好数据
        """
        try:
            # 查询用户旅行画像
            query = """
            SELECT user_id, travel_style, accommodation_pref, transportation_pref,
                   travel_summary, travel_keywords, updated_at
            FROM dh_tripplanner.user_travel_profiles
            WHERE user_id = %s
            """

            result = await fetch_one(query, (user_id,))

            if result:
                self.logger.info(f"成功获取用户 {user_id} 的旅行偏好")
                return {
                    "user_id": result["user_id"],
                    "travel_style": result["travel_style"],
                    "accommodation_pref": result["accommodation_pref"],
                    "transportation_pref": result["transportation_pref"],
                    "travel_summary": result["travel_summary"],
                    "travel_keywords": result["travel_keywords"],
                    "updated_at": result["updated_at"].isoformat() if result["updated_at"] else None
                }
            else:
                self.logger.warning(f"未找到用户 {user_id} 的旅行偏好")
                return None

        except Exception as e:
            self.logger.error(f"查询用户 {user_id} 旅行偏好失败: {str(e)}")
            return None
    
    def format_user_profile_for_analysis(self, profile: Dict[str, Any]) -> str:
        """
        将用户画像格式化为适合AI分析的文本
        
        Args:
            profile: 用户综合画像数据
            
        Returns:
            格式化的用户画像文本
        """
        user_id = profile.get("user_id", "未知")
        
        # 构建用户画像文本
        profile_text = f"用户ID: {user_id}\n"
        
        # 添加用户摘要
        if profile.get("user_summary"):
            summary = profile["user_summary"]["summary"]
            keywords = ", ".join(profile["user_summary"]["keywords"])
            profile_text += f"用户画像摘要: {summary}\n"
            profile_text += f"关键词: {keywords}\n"
        
        # 添加记忆信息
        if profile.get("user_memories"):
            profile_text += f"用户记忆 ({len(profile['user_memories'])}条):\n"
            for i, memory in enumerate(profile["user_memories"][:5], 1):
                profile_text += f"  {i}. {memory['content']} (置信度: {memory['confidence']:.2f})\n"
        
        # 添加旅行历史
        if profile.get("travel_history"):
            profile_text += f"旅行历史 ({len(profile['travel_history'])}次):\n"
            for i, travel in enumerate(profile["travel_history"][:3], 1):
                profile_text += f"  {i}. {travel['title']} - {travel['city_name']} ({travel['total_days']}天)\n"
        
        # 添加分析上下文
        context = profile.get("analysis_context", {})
        if context.get("travel_patterns"):
            patterns = context["travel_patterns"]
            profile_text += f"旅行模式: 平均{patterns['average_trip_days']}天/次，共{patterns['total_trips']}次旅行\n"
        
        profile_text += f"画像完整度: {profile.get('profile_completeness', 0):.2f}\n"
        
        return profile_text


# 全局服务实例
_user_profile_db_service = None

def get_user_profile_database_service() -> UserProfileDatabaseService:
    """获取用户画像数据库服务实例"""
    global _user_profile_db_service
    if _user_profile_db_service is None:
        _user_profile_db_service = UserProfileDatabaseService()
    return _user_profile_db_service
