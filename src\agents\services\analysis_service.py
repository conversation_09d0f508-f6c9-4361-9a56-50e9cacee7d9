"""
分析服务 (Analysis Service)

提供各种分析功能的原子化服务，包括意图分析、偏好分析等。
"""

import json
import logging
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime

from src.prompts import apply_prompt_template
from .reasoning_service import ReasoningService
from .memory_service import MemoryService

logger = logging.getLogger(__name__)


class AnalysisService:
    """分析服务类"""
    
    def __init__(self):
        self.reasoning_service = ReasoningService()
        self.memory_service = MemoryService()
    
    async def analyze_core_intent(
        self,
        original_query: str,
        user_id: str,
        user_profile: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        分析用户的核心旅行意图
        
        Args:
            original_query: 用户原始查询
            user_id: 用户ID
            user_profile: 用户画像信息
            
        Returns:
            核心意图分析结果
        """
        try:
            logger.info(f"开始核心意图分析 - 用户: {user_id}")
            
            # 获取用户历史记忆
            user_memories = await self.memory_service.get_user_memories(user_id)
            
            # 构建提示词变量
            template_vars = {
                "original_query": original_query,
                "user_profile": user_profile,
                "user_memories": user_memories
            }
            
            # 应用提示词模板
            messages = apply_prompt_template(
                "travel_planner/01_core_intent_analyzer",
                template_vars
            )
            
            # 调用推理服务
            core_intent = await self.reasoning_service.analyze_with_structured_output(
                messages=messages,
                response_format="core_intent_schema"
            )
            
            logger.info(f"核心意图分析完成 - 目的地: {core_intent.get('destinations')}")
            return core_intent
            
        except Exception as e:
            logger.error(f"核心意图分析失败: {str(e)}")
            raise
    
    async def analyze_multi_city_strategy(
        self,
        destinations: List[str],
        total_days: int,
        user_preferences: Optional[Dict[str, Any]] = None,
        transportation_mode: Optional[str] = None,
        distance_matrix: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        分析多城市旅行策略
        
        Args:
            destinations: 目的地列表
            total_days: 总天数
            user_preferences: 用户偏好
            transportation_mode: 交通方式
            distance_matrix: 距离矩阵
            
        Returns:
            多城市策略分析结果
        """
        try:
            logger.info(f"开始多城市策略分析 - 目的地数量: {len(destinations)}")
            
            # 如果只有一个目的地，返回空结果
            if len(destinations) <= 1:
                return {}
            
            # 构建提示词变量
            template_vars = {
                "destinations": destinations,
                "total_days": total_days,
                "distance_matrix": distance_matrix,
                "user_preferences": user_preferences,
                "transportation_mode": transportation_mode
            }
            
            # 应用提示词模板
            messages = apply_prompt_template(
                "travel_planner/01a_multi_city_strategy_analyzer",
                template_vars
            )
            
            # 调用推理服务
            strategy = await self.reasoning_service.analyze_with_structured_output(
                messages=messages,
                response_format="multi_city_strategy_schema"
            )
            
            logger.info(f"多城市策略分析完成 - 策略类型: {strategy.get('strategy_type')}")
            return strategy
            
        except Exception as e:
            logger.error(f"多城市策略分析失败: {str(e)}")
            raise
    
    async def analyze_driving_context(
        self,
        vehicle_info: Optional[Dict[str, Any]],
        destinations: List[str],
        total_days: int,
        user_preferences: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        分析驾驶情境和策略
        
        Args:
            vehicle_info: 车辆信息
            destinations: 目的地列表
            total_days: 总天数
            user_preferences: 用户偏好
            
        Returns:
            驾驶情境分析结果
        """
        try:
            logger.info("开始驾驶情境分析")
            
            # 构建提示词变量
            template_vars = {
                "user_vehicle_info": vehicle_info,
                "destinations": destinations,
                "total_days": total_days,
                "user_preferences": user_preferences
            }
            
            # 应用提示词模板
            messages = apply_prompt_template(
                "travel_planner/01b_driving_context_analyzer",
                template_vars
            )
            
            # 调用推理服务
            response = await self.reasoning_service.analyze_with_structured_output(
                messages=messages,
                response_format="driving_context_schema"
            )
            
            # 解析结果
            driving_context = json.loads(response.content)
            
            logger.info(f"驾驶情境分析完成 - 策略: {driving_context.get('driving_strategy')}")
            return driving_context
            
        except Exception as e:
            logger.error(f"驾驶情境分析失败: {str(e)}")
            raise
    
    async def analyze_preferences(
        self,
        core_intent: Dict[str, Any],
        user_profile: Optional[Dict[str, Any]] = None,
        user_memories: Optional[List[Dict[str, Any]]] = None
    ) -> Dict[str, Any]:
        """
        分析用户偏好（景点、美食等）
        
        Args:
            core_intent: 核心意图分析结果
            user_profile: 用户画像
            user_memories: 用户历史记忆
            
        Returns:
            偏好分析结果
        """
        try:
            logger.info("开始偏好分析")
            
            # 分析景点偏好
            attraction_template_vars = {
                "core_intent": core_intent,
                "user_profile": user_profile,
                "user_memories": user_memories,
                "destinations": core_intent.get("destinations"),
                "days": core_intent.get("days")
            }
            
            attraction_messages = apply_prompt_template(
                "travel_planner/02_attraction_preference_analyzer",
                attraction_template_vars
            )
            
            # 分析美食偏好
            food_template_vars = {
                "core_intent": core_intent,
                "user_profile": user_profile,
                "user_memories": user_memories,
                "destinations": core_intent.get("destinations"),
                "days": core_intent.get("days"),
                "travelers": core_intent.get("travelers")
            }
            
            food_messages = apply_prompt_template(
                "travel_planner/03_food_preference_analyzer",
                food_template_vars
            )
            
            # 并行分析景点和美食偏好
            attraction_response = await self.reasoning_service.analyze_with_structured_output(
                messages=attraction_messages,
                response_format="attraction_preference_schema"
            )
            
            food_response = await self.reasoning_service.analyze_with_structured_output(
                messages=food_messages,
                response_format="food_preference_schema"
            )
            
            # 解析结果
            attraction_preferences = json.loads(attraction_response.content)
            food_preferences = json.loads(food_response.content)
            
            # 构建偏好画像
            preference_profile = {
                "attraction_preferences": attraction_preferences,
                "food_preferences": food_preferences,
                "confidence_score": (
                    attraction_preferences.get("confidence_score", 0) +
                    food_preferences.get("confidence_score", 0)
                ) / 2
            }
            
            logger.info("偏好分析完成")
            return preference_profile
            
        except Exception as e:
            logger.error(f"偏好分析失败: {str(e)}")
            raise
    
    async def generate_itinerary(
        self,
        core_intent: Dict[str, Any],
        preference_profile: Dict[str, Any],
        multi_city_strategy: Optional[Dict[str, Any]] = None,
        driving_context: Optional[Dict[str, Any]] = None,
        planning_mode: str = "general_assistance"
    ) -> Dict[str, Any]:
        """
        生成详细行程
        
        Args:
            core_intent: 核心意图分析结果
            preference_profile: 偏好画像
            multi_city_strategy: 多城市策略（可选）
            driving_context: 驾驶情境（可选）
            planning_mode: 规划模式
            
        Returns:
            详细行程安排
        """
        try:
            logger.info("开始生成行程")
            
            # 构建提示词变量
            template_vars = {
                "core_intent": core_intent,
                "preference_profile": preference_profile,
                "multi_city_strategy": multi_city_strategy,
                "driving_context": driving_context,
                "planning_mode": planning_mode
            }
            
            # 应用提示词模板
            messages = apply_prompt_template(
                "travel_planner/04_itinerary_generator",
                template_vars
            )
            
            # 调用推理服务
            itinerary = await self.reasoning_service.analyze_with_structured_output(
                messages=messages,
                response_format="itinerary_schema"
            )
            
            logger.info("行程生成完成")
            return itinerary
            
        except Exception as e:
            logger.error(f"行程生成失败: {str(e)}")
            raise
