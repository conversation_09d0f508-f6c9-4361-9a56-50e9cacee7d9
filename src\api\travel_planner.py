"""
旅行规划API接口

提供旅行规划的RESTful API和SSE流式接口，支持实时进度反馈。
"""
import asyncio
import json
import uuid
from typing import Dict, Any, Optional
from datetime import datetime

from fastapi import APIRouter, HTTPException, Depends, Request
from fastapi.responses import StreamingResponse
from pydantic import BaseModel

from src.models.travel_planner import (
    TravelPlanRequest, TravelItinerary, StreamEvent, EventType, UserProfile
)
from src.models.mysql_crud import (
    ai_planning_session_crud
)
# 使用LangGraph重构的Agent
from src.agents.travel_planner_agent_langgraph import TravelPlannerAgentLangGraph
from src.database.mongodb_client import get_mongo_client
from src.database.mysql_client import get_db
from src.core.logger import get_logger

logger = get_logger("travel_planner_api")
router = APIRouter(prefix="/api/travel", tags=["旅行规划"])


class PlanRequest(BaseModel):
    """规划请求模型"""
    query: str
    user_id: str
    session_id: Optional[str] = None


class PlanResponse(BaseModel):
    """规划响应模型"""
    trace_id: str
    status: str
    message: str


@router.post("/plan", response_model=PlanResponse)
async def create_travel_plan(request: PlanRequest):
    """
    创建旅行规划任务
    
    创建一个新的旅行规划任务，返回trace_id用于后续查询进度。
    """
    try:
        trace_id = str(uuid.uuid4())
        
        # 记录规划请求
        # 在 ai_planning_sessions 表中插入一条新记录
        async with get_db() as db:
            await ai_planning_session_crud.create_session(db, session_data={
                'id': trace_id,
                'user_id': request.user_id,
                # 'session_id': request.session_id,
                'user_input': request.query,
                'created_at': datetime.now()
            })
        # 在 ai_interaction_logs 集合中插入一个"骨架"文档
        mongo_client = await get_mongo_client()
        await mongo_client.create_interaction_log({
            "user_id": request.user_id,
            "interaction_id": trace_id,
            "status": "PROCESSING"
        })
        await mongo_client.log_analytics({
            "event_type": "plan_request",
            "user_id": request.user_id,
            "trace_id": trace_id,
            "properties": {
                "query": request.query,
                "session_id": request.session_id
            }
        })
        
        logger.info(f"创建旅行规划任务: {trace_id}", extra={
            "trace_id": trace_id,
            "user_id": request.user_id,
            "query": request.query
        })
        
        return PlanResponse(
            trace_id=trace_id,
            status="created",
            message="规划任务已创建，请使用SSE接口获取实时进度"
        )
        
    except Exception as e:
        logger.error(f"创建规划任务失败: {str(e)}")
        raise HTTPException(status_code=500, detail="创建规划任务失败")


@router.get("/plan/{trace_id}/stream")
async def stream_travel_plan(trace_id: str, user_id: str, query: str, phase: str = "analysis"):
    """
    流式获取旅行规划进度
    
    通过Server-Sent Events (SSE) 实时获取旅行规划的进度和结果。
    """
    
    async def event_generator():
        """事件生成器"""
        try:
            # 创建LangGraph旅行规划Agent
            agent = TravelPlannerAgentLangGraph(enable_interaction_hooks=True)

            # 创建规划请求
            plan_request = TravelPlanRequest(
                user_id=user_id,
                query=query,
                trace_id=trace_id
            )

            # 发送开始事件
            start_event_data = {
                "trace_id": trace_id,
                "event_type": "start",
                "payload": {
                    "message": "开始规划旅行行程"
                },
                "timestamp": datetime.now().isoformat()
            }
            yield f"data: {json.dumps(start_event_data, ensure_ascii=False)}\n\n"

            # 执行规划并流式返回结果
            # LangGraph Agent使用不同的接口
            if phase == "analysis":
                # 分析阶段：使用交互式流式规划
                stream_generator = agent.plan_travel_stream_interactive(
                    user_id=user_id,
                    query=query,
                    session_id=trace_id
                )
            else:
                # 规划阶段：使用全自动流式规划
                stream_generator = agent.plan_travel_stream_automatic(
                    user_id=user_id,
                    query=query,
                    session_id=trace_id
                )

            async for sse_event_str in stream_generator:
                # LangGraph Agent已经返回格式化的SSE字符串，直接发送
                yield sse_event_str

                # 尝试解析事件以检查是否需要保存到数据库
                try:
                    # 从SSE字符串中提取JSON数据
                    if sse_event_str.startswith("data: "):
                        json_str = sse_event_str[6:].strip()
                        if json_str and json_str != "\n":
                            event_data = json.loads(json_str)

                            # 如果是最终行程，保存到数据库
                            if event_data.get("event_type") == "final_itinerary":
                                try:
                                    mongo_client = await get_mongo_client()
                                    # 转换TravelItinerary数据为ItineraryDocument兼容格式
                                    itinerary_data = event_data.get("data", {}).copy()

                                    # 从summary中提取字段到顶层
                                    if "summary" in itinerary_data:
                                        summary = itinerary_data["summary"]
                                        itinerary_data["title"] = summary.get("title", "未命名行程")
                                        itinerary_data["destination_city"] = summary.get("destination_city", "未知目的地")
                                        itinerary_data["days"] = summary.get("days", 3)
                                        itinerary_data["tags"] = summary.get("tags", [])

                                    await mongo_client.create_itinerary(itinerary_data)
                                    logger.info(f"行程已保存到数据库: {trace_id}")
                                except Exception as e:
                                    logger.error(f"保存行程失败: {str(e)}")
                except Exception as e:
                    # 忽略解析错误，继续处理
                    pass

                # 添加小延迟，避免过快发送
                await asyncio.sleep(0.1)
            
            # 发送完成事件
            complete_event_data = {
                "trace_id": trace_id,
                "event_type": "complete",
                "payload": {
                    "message": "旅行规划完成"
                },
                "timestamp": datetime.now().isoformat()
            }
            yield f"data: {json.dumps(complete_event_data, ensure_ascii=False)}\n\n"
            
        except Exception as e:
            logger.error(f"规划过程中发生错误: {str(e)}", extra={
                "trace_id": trace_id,
                "error": str(e)
            })
            
            # 发送错误事件
            error_event_data = {
                "trace_id": trace_id,
                "event_type": "error",
                "payload": {
                    "error_message": str(e)
                },
                "timestamp": datetime.now().isoformat()
            }
            yield f"data: {json.dumps(error_event_data, ensure_ascii=False)}\n\n"
    
    return StreamingResponse(
        event_generator(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "Cache-Control"
        }
    )


@router.post("/plan/{trace_id}/start_planning")
async def start_planning_phase(
    trace_id: str,
    analysis_result: Dict[str, Any]
):
    """
    启动规划阶段

    基于分析阶段的结果，启动详细的行程规划阶段。

    Args:
        trace_id: 会话ID
        analysis_result: 分析阶段的结果数据
    """
    try:
        logger.info(f"启动规划阶段 - Session: {trace_id}")

        # 创建Agent实例
        agent = TravelPlannerAgentLangGraph()

        # 启动规划阶段的流式处理
        async def generate_planning_stream():
            try:
                async for event in agent.start_planning_phase(trace_id, analysis_result):
                    yield event
            except Exception as e:
                logger.error(f"规划阶段流式处理失败: {str(e)}")
                error_event = {
                    "event_type": "error",
                    "payload": {
                        "error_message": f"规划失败: {str(e)}",
                        "stage": "planning"
                    }
                }
                yield f"data: {json.dumps(error_event, ensure_ascii=False)}\n\n"

        return StreamingResponse(
            generate_planning_stream(),
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
                "Access-Control-Allow-Headers": "Cache-Control"
            }
        )

    except Exception as e:
        logger.error(f"启动规划阶段失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"启动规划失败: {str(e)}")


@router.get("/plan/{trace_id}")
async def get_travel_plan(trace_id: str):
    """
    获取旅行规划结果
    
    根据trace_id获取完整的旅行规划结果。
    """
    try:
        mongo_client = await get_mongo_client()
        itinerary = await mongo_client.get_itinerary(trace_id)
        
        if not itinerary:
            raise HTTPException(status_code=404, detail="未找到指定的行程")
        
        return {
            "code": 200,
            "data": itinerary,
            "message": "获取成功"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取行程失败: {str(e)}")
        raise HTTPException(status_code=500, detail="获取行程失败")


@router.get("/user/{user_id}/plans")
async def get_user_plans(
    user_id: str,
    limit: int = 20,
    skip: int = 0,
    status: Optional[str] = None
):
    """
    获取用户的旅行规划列表
    
    分页获取指定用户的所有旅行规划。
    """
    try:
        mongo_client = await get_mongo_client()
        itineraries = await mongo_client.get_user_itineraries(
            user_id=user_id,
            limit=limit,
            skip=skip,
            status=status
        )
        
        return {
            "code": 200,
            "data": {
                "items": itineraries,
                "total": len(itineraries),
                "limit": limit,
                "skip": skip
            },
            "message": "获取成功"
        }
        
    except Exception as e:
        logger.error(f"获取用户行程列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail="获取行程列表失败")


@router.put("/plan/{trace_id}")
async def update_travel_plan(trace_id: str, update_data: Dict[str, Any]):
    """
    更新旅行规划
    
    更新指定的旅行规划信息。
    """
    try:
        mongo_client = await get_mongo_client()
        success = await mongo_client.update_itinerary(trace_id, update_data)
        
        if not success:
            raise HTTPException(status_code=404, detail="未找到指定的行程")
        
        return {
            "code": 200,
            "message": "更新成功"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新行程失败: {str(e)}")
        raise HTTPException(status_code=500, detail="更新行程失败")


@router.post("/feedback")
async def submit_feedback(feedback_data: Dict[str, Any]):
    """
    提交反馈
    
    用户对旅行规划结果的反馈，用于改进算法。
    """
    try:
        mongo_client = await get_mongo_client()
        
        # 创建反馈记录
        feedback_id = await mongo_client.create_memory({
            "user_id": feedback_data.get("user_id"),
            "memory_type": "feedback",
            "content": feedback_data.get("content", ""),
            "context": feedback_data,
            "importance_score": 2.0,  # 反馈的重要性较高
            "related_itinerary_id": feedback_data.get("trace_id")
        })
        
        # 记录分析数据
        await mongo_client.log_analytics({
            "event_type": "feedback_submitted",
            "user_id": feedback_data.get("user_id"),
            "properties": feedback_data
        })
        
        return {
            "code": 200,
            "data": {"feedback_id": feedback_id},
            "message": "反馈提交成功"
        }
        
    except Exception as e:
        logger.error(f"提交反馈失败: {str(e)}")
        raise HTTPException(status_code=500, detail="提交反馈失败")


@router.get("/health")
async def health_check():
    """健康检查接口"""
    try:
        # 检查数据库连接
        mongo_client = await get_mongo_client()
        
        return {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "services": {
                "mongodb": "connected",
                "agent": "ready"
            }
        }
        
    except Exception as e:
        logger.error(f"健康检查失败: {str(e)}")
        return {
            "status": "unhealthy",
            "timestamp": datetime.now().isoformat(),
            "error": str(e)
        }


@router.get("/user_profile")
async def get_user_profile(user_id: str = "1"):
    """
    获取用户画像信息

    Args:
        user_id: 用户ID，默认为"1"

    Returns:
        用户画像数据，包括基本信息、偏好标签、预算偏好等
    """
    try:
        # 直接从数据库获取用户画像和记忆
        from src.models.mysql_crud import user_memory_crud, user_summary_crud, itinerary_crud
        from src.database.mysql_client import get_db

        async with get_db() as db:
            # 获取用户记忆
            user_memories = await user_memory_crud.get_by_user(db, user_id=user_id)

            # 获取用户画像总结
            user_summary = await user_summary_crud.get_by_user(db, user_id=user_id)

            # 获取历史行程
            itineraries = await itinerary_crud.get_by_user(db, user_id=user_id)

            # 构建完整的用户画像
            preferences = {}
            tags = []

            # 从用户总结中提取偏好
            if user_summary:
                summary_data = user_summary.summary_data if hasattr(user_summary, 'summary_data') else {}
                if isinstance(summary_data, dict):
                    preferences.update(summary_data.get('preferences', {}))
                    tags.extend(summary_data.get('tags', []))

            # 从历史记忆中提取标签和偏好
            for memory in user_memories:
                if hasattr(memory, 'keywords') and memory.keywords:
                    tags.extend(memory.keywords)
                if hasattr(memory, 'content') and memory.content:
                    # 简单的关键词提取
                    content = memory.content.lower()
                    if '亲子' in content or '孩子' in content:
                        tags.append('亲子')
                    if '历史' in content or '文化' in content:
                        tags.append('历史文化')
                    if '美食' in content:
                        tags.append('美食')
                    if '自然' in content or '风景' in content:
                        tags.append('自然风光')

            # 设置默认偏好
            if not preferences:
                preferences = {
                    'travel_style': '休闲',
                    'budget_preference': '中等',
                    'preferred_trip_duration': 3,
                    'age_group': '25-35岁',
                    'gender': '未知',
                    'occupation': '白领',
                    'travel_companion': '个人'
                }

            # 去重标签
            tags = list(set(tags))[:15]
            if not tags:
                tags = ['休闲', '舒适', '性价比']

            user_profile_data = {
                'preferences': preferences,
                'tags': tags,
                'budget_preference': preferences.get('budget_preference', '中等'),
                'travel_style': preferences.get('travel_style', '休闲')
            }

        if not user_profile_data:
            # 返回默认用户画像
            return {
                "basic_info": {
                    "age": "25-35岁",
                    "gender": "未知",
                    "occupation": "白领",
                    "travel_companion": "个人"
                },
                "tags": ["休闲", "舒适"],
                "budget_preference": "中等",
                "preferences": {
                    "travel_style": "休闲",
                    "season": "春季",
                    "duration": "3天"
                },
                "recommendation_reason": "基于默认偏好为您推荐适合的旅行方案。"
            }

        # 解析用户画像数据
        preferences = user_profile_data.get('preferences', {})
        tags = user_profile_data.get('tags', [])

        # 构建基本信息
        basic_info = {
            "age": preferences.get('age_group', '25-35岁'),
            "gender": preferences.get('gender', '未知'),
            "occupation": preferences.get('occupation', '白领'),
            "travel_companion": preferences.get('travel_companion', '个人')
        }

        # 构建返回数据
        profile_response = {
            "basic_info": basic_info,
            "tags": tags[:8],  # 限制标签数量
            "budget_preference": user_profile_data.get('budget_preference', '中等'),
            "preferences": {
                "travel_style": user_profile_data.get('travel_style', '休闲'),
                "season": preferences.get('preferred_season', '春季'),
                "duration": f"{preferences.get('preferred_trip_duration', 3)}天"
            },
            "recommendation_reason": f"根据您的{len(user_memories)}次历史旅行记录和个人偏好，为您推荐最适合的旅行方案。"
        }

        logger.info(f"成功获取用户{user_id}的画像信息")
        return profile_response

    except Exception as e:
        logger.error(f"获取用户画像失败: {str(e)}", exc_info=True)
        # 返回默认画像作为后备
        return {
            "basic_info": {
                "age": "25-35岁",
                "gender": "未知",
                "occupation": "白领",
                "travel_companion": "个人"
            },
            "tags": ["休闲", "舒适"],
            "budget_preference": "中等",
            "preferences": {
                "travel_style": "休闲",
                "season": "春季",
                "duration": "3天"
            },
            "recommendation_reason": "基于默认偏好为您推荐适合的旅行方案。"
        }
