"""
地图API接口

提供POI搜索、地理编码等地图相关的RESTful API接口。
"""
from typing import List, Optional
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel

from tools.Amap.map_tool import MapTool
from src.core.logger import get_logger

logger = get_logger("map_api")
router = APIRouter(prefix="/api/map", tags=["地图"])


class POISearchRequest(BaseModel):
    """POI搜索请求模型"""
    keywords: str
    city: Optional[str] = None
    page_size: Optional[int] = 10


class GeocodeRequest(BaseModel):
    """地理编码请求模型"""
    address: str


@router.post("/search_pois")
async def search_pois(request: POISearchRequest):
    """
    搜索POI信息

    Args:
        request: 包含keywords、city和page_size的请求数据
    """
    try:
        keywords = request.keywords
        city = request.city
        page_size = request.page_size or 10

        logger.info(f"搜索POI: 关键词={keywords}, 城市={city}, 数量={page_size}")

        # 初始化高德地图工具（使用默认API密钥）
        map_tool = MapTool()

        # 搜索POI
        poi_results = map_tool.search_pois(
            keywords=keywords,
            city=city,
            page_size=page_size
        )

        # 转换POI数据格式
        pois = []
        for poi in poi_results:
            poi_data = {
                "id": getattr(poi, 'id', ''),
                "name": getattr(poi, 'name', ''),
                "address": getattr(poi, 'address', ''),
                "type": getattr(poi, 'type', ''),
                "location": {
                    "longitude": getattr(poi.location, 'longitude', 0) if hasattr(poi, 'location') else 0,
                    "latitude": getattr(poi.location, 'latitude', 0) if hasattr(poi, 'location') else 0
                },
                "distance": getattr(poi, 'distance', 0),
                "rating": getattr(poi, 'rating', 0),
                "price": getattr(poi, 'price', 0),
                "phone": getattr(poi, 'phone', ''),
                "photos": getattr(poi, 'photos', []) or [],
                "image": getattr(poi, 'image', ''),
                "business_hours": getattr(poi, 'business_hours', ''),
                "price_range": getattr(poi, 'price_range', '')
            }
            pois.append(poi_data)

        return {
            "status": "success",
            "pois": pois,
            "total": len(pois),
            "description": f"找到{len(pois)}个相关POI"
        }

    except Exception as e:
        logger.error(f"POI搜索失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"POI搜索失败: {str(e)}")


@router.post("/geocode")
async def geocode_address(request: GeocodeRequest):
    """
    地理编码（地址转坐标）

    Args:
        request: 包含address的请求数据
    """
    try:
        address = request.address

        logger.info(f"地理编码: 地址={address}")

        # 初始化高德地图工具（使用默认API密钥）
        map_tool = MapTool()

        # 地理编码
        geo_result = map_tool.geocode_address(address)

        if geo_result.get("status") == "1" and geo_result.get("geocodes"):
            geocode = geo_result["geocodes"][0]
            return {
                "status": "success",
                "geocode": {
                    "formatted_address": geocode.get("formatted_address", ""),
                    "province": geocode.get("province", ""),
                    "city": geocode.get("city", ""),
                    "district": geocode.get("district", ""),
                    "adcode": geocode.get("adcode", ""),
                    "location": geocode.get("location", ""),
                    "level": geocode.get("level", "")
                },
                "description": f"已获取{address}的地理编码信息"
            }
        else:
            return {
                "status": "error",
                "geocode": {},
                "description": f"无法获取{address}的地理编码信息"
            }

    except Exception as e:
        logger.error(f"地理编码失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"地理编码失败: {str(e)}")


@router.get("/poi_detail/{poi_id}")
async def get_poi_detail(poi_id: str):
    """
    获取POI详细信息

    Args:
        poi_id: POI的ID
    """
    try:
        logger.info(f"获取POI详情: ID={poi_id}")

        # 初始化高德地图工具（使用默认API密钥）
        map_tool = MapTool()

        # 获取POI详情
        poi_detail = map_tool.place_detail_by_id(poi_id)

        if poi_detail.get("status") == "1" and poi_detail.get("pois"):
            poi = poi_detail["pois"][0]
            return {
                "status": "success",
                "poi": {
                    "id": poi.get("id", ""),
                    "name": poi.get("name", ""),
                    "address": poi.get("address", ""),
                    "type": poi.get("type", ""),
                    "location": poi.get("location", ""),
                    "tel": poi.get("tel", ""),
                    "photos": poi.get("photos", []),
                    "business": poi.get("business", {}),
                    "rating": poi.get("rating", 0),
                    "cost": poi.get("cost", ""),
                    "recommend": poi.get("recommend", "")
                },
                "description": f"已获取POI详细信息"
            }
        else:
            return {
                "status": "error",
                "poi": {},
                "description": f"无法获取POI详细信息"
            }

    except Exception as e:
        logger.error(f"POI详情获取失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"POI详情获取失败: {str(e)}")


@router.get("/health")
async def health_check():
    """健康检查端点"""
    return {
        "status": "healthy",
        "service": "map_api",
        "description": "地图API服务正常运行"
    }
