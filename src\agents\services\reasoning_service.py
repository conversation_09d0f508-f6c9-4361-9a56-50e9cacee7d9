"""
推理服务 (Reasoning Service)

提供LLM推理能力的原子化服务，支持结构化输出和多种推理模式。
"""

import json
import logging
from typing import Dict, List, Any, Optional, Union
from datetime import datetime

from langchain_openai import ChatOpenAI
from langchain_core.messages import HumanMessage, SystemMessage, AIMessage
from langchain_core.output_parsers import JsonOutputParser
from langchain_core.prompts import ChatPromptTemplate

from src.core.config import get_settings
from src.core.llm_manager import LLMManager

logger = logging.getLogger(__name__)


class ReasoningService:
    """推理服务类"""

    def __init__(self, llm_role: str = "reasoning", temperature: float = 0.1):
        """
        初始化推理服务

        Args:
            llm_role: LLM角色，支持 'reasoning', 'basic', 'map'
            temperature: 温度参数
        """
        self.llm_role = llm_role
        self.temperature = temperature
        self.settings = get_settings()
        self.llm_manager = LLMManager()

        # 获取配置的LLM客户端
        llm_config = self.settings.get_llm_config_by_role(llm_role)

        # 创建LangChain兼容的ChatOpenAI实例
        self.llm = ChatOpenAI(
            model=llm_config.model,
            api_key=llm_config.api_key,
            base_url=llm_config.base_url,
            temperature=temperature,
            max_tokens=4000
        )
        
        # 定义结构化输出的Schema
        self.schemas = {
            "core_intent_schema": {
                "type": "object",
                "properties": {
                    "destinations": {"type": "array", "items": {"type": "string"}},
                    "days": {"type": "integer"},
                    "travel_dates": {"type": "object"},
                    "transportation": {"type": "object"},
                    "travel_theme": {"type": "string"},
                    "budget": {"type": "object"},
                    "travelers": {"type": "object"},
                    "preferences": {"type": "object"},
                    "extracted_keywords": {"type": "array", "items": {"type": "string"}},
                    "confidence_score": {"type": "number"},
                    "missing_info": {"type": "array", "items": {"type": "string"}}
                },
                "required": ["destinations", "days", "confidence_score"]
            },
            "multi_city_strategy_schema": {
                "type": "object",
                "properties": {
                    "strategy_type": {"type": "string"},
                    "recommended_order": {"type": "array", "items": {"type": "string"}},
                    "time_allocation": {"type": "array"},
                    "transportation_plan": {"type": "array"},
                    "accommodation_strategy": {"type": "object"},
                    "highlights": {"type": "object"},
                    "total_travel_time": {"type": "string"},
                    "estimated_transport_cost": {"type": "string"},
                    "flexibility_score": {"type": "integer"},
                    "recommendation_confidence": {"type": "number"}
                },
                "required": ["strategy_type", "recommended_order", "recommendation_confidence"]
            },
            "driving_context_schema": {
                "type": "object",
                "properties": {
                    "driving_strategy": {"type": "string"},
                    "vehicle_analysis": {"type": "object"},
                    "range_planning": {"type": "object"},
                    "driving_constraints": {"type": "object"},
                    "charging_strategy": {"type": "object"},
                    "route_preferences": {"type": "object"},
                    "user_guidance": {"type": "object"},
                    "confidence_score": {"type": "number"},
                    "requires_clarification": {"type": "array", "items": {"type": "string"}}
                },
                "required": ["driving_strategy", "confidence_score"]
            },
            "attraction_preference_schema": {
                "type": "object",
                "properties": {
                    "attraction_type_preferences": {"type": "object"},
                    "touring_style": {"type": "object"},
                    "time_and_energy": {"type": "object"},
                    "special_considerations": {"type": "object"},
                    "filtering_criteria": {"type": "object"},
                    "personalization_factors": {"type": "object"},
                    "confidence_score": {"type": "number"},
                    "recommendation_notes": {"type": "string"}
                },
                "required": ["attraction_type_preferences", "confidence_score"]
            },
            "food_preference_schema": {
                "type": "object",
                "properties": {
                    "taste_preferences": {"type": "object"},
                    "cuisine_preferences": {"type": "object"},
                    "restaurant_type_preferences": {"type": "object"},
                    "dining_environment": {"type": "object"},
                    "special_requirements": {"type": "object"},
                    "meal_planning": {"type": "object"},
                    "local_food_exploration": {"type": "object"},
                    "filtering_criteria": {"type": "object"},
                    "confidence_score": {"type": "number"},
                    "recommendation_notes": {"type": "string"}
                },
                "required": ["taste_preferences", "confidence_score"]
            },
            "itinerary_schema": {
                "type": "object",
                "properties": {
                    "itinerary_summary": {"type": "object"},
                    "daily_itineraries": {"type": "array"},
                    "transportation_details": {"type": "object"},
                    "recommendations": {"type": "object"},
                    "flexibility_options": {"type": "object"}
                },
                "required": ["itinerary_summary", "daily_itineraries"]
            }
        }
    
    async def analyze_with_structured_output(
        self,
        messages: List[Dict[str, str]],
        response_format: str,
        max_retries: int = 3
    ) -> Any:
        """
        使用结构化输出进行分析
        
        Args:
            messages: 消息列表
            response_format: 响应格式Schema名称
            max_retries: 最大重试次数
            
        Returns:
            结构化分析结果
        """
        try:
            logger.info(f"开始结构化推理 - 格式: {response_format}")
            
            # 获取对应的Schema
            schema = self.schemas.get(response_format)
            if not schema:
                raise ValueError(f"未知的响应格式: {response_format}")
            
            # 构建消息
            chat_messages = []
            for msg in messages:
                if msg["role"] == "system":
                    chat_messages.append(SystemMessage(content=msg["content"]))
                elif msg["role"] == "user":
                    chat_messages.append(HumanMessage(content=msg["content"]))
                elif msg["role"] == "assistant":
                    chat_messages.append(AIMessage(content=msg["content"]))
            
            # 添加JSON格式要求
            json_instruction = f"""
请严格按照以下JSON Schema格式返回结果，不要包含任何其他文本：

{json.dumps(schema, indent=2, ensure_ascii=False)}

返回的JSON必须是有效的JSON格式，可以被json.loads()解析。
"""
            
            chat_messages.append(HumanMessage(content=json_instruction))
            
            # 调用LLM
            for attempt in range(max_retries):
                try:
                    response = await self.llm.ainvoke(chat_messages)

                    # 尝试解析JSON
                    content = response.content.strip()

                    # 添加调试日志
                    logger.info(f"LLM原始响应内容长度: {len(content)}")
                    logger.info(f"LLM原始响应内容前500字符: {content[:500]}")

                    # 移除可能的markdown代码块标记
                    if content.startswith("```json"):
                        content = content[7:]
                    if content.endswith("```"):
                        content = content[:-3]

                    # 移除可能的思考标签
                    if content.startswith("<think>"):
                        # 查找</think>标签的结束位置
                        think_end = content.find("</think>")
                        if think_end != -1:
                            content = content[think_end + 8:]  # 8 = len("</think>")
                        else:
                            # 如果没有找到结束标签，查找JSON开始的位置
                            logger.warning(f"发现<think>标签但没有找到</think>结束标签，尝试提取JSON")
                            # 查找```json标记
                            json_marker = content.find("```json")
                            if json_marker != -1:
                                content = content[json_marker + 7:]  # 7 = len("```json")
                                logger.info(f"从```json标记开始提取JSON内容")
                            else:
                                # 查找第一个{字符
                                json_start = content.find("{")
                                if json_start != -1:
                                    content = content[json_start:]
                                    logger.info(f"从位置{json_start}开始提取JSON内容")
                                else:
                                    logger.warning("在响应中没有找到JSON开始标记，跳过此次尝试")
                                    if attempt == max_retries - 1:
                                        raise ValueError("响应中没有找到有效的JSON内容")
                                    continue

                    content = content.strip()
                    logger.info(f"处理后的内容长度: {len(content)}")
                    logger.info(f"处理后的内容前200字符: {content[:200]}")

                    # 如果内容以```json开头，移除markdown标记
                    if content.startswith("```json"):
                        content = content[7:]
                    if content.endswith("```"):
                        content = content[:-3]
                    content = content.strip()

                    logger.info(f"最终JSON内容长度: {len(content)}")
                    logger.info(f"最终JSON内容: {content}")

                    # 检查内容是否为空
                    if not content:
                        logger.warning(f"LLM返回空内容 (尝试 {attempt + 1})")
                        if attempt == max_retries - 1:
                            raise ValueError("LLM返回空内容")
                        continue

                    # 验证JSON格式
                    parsed_result = json.loads(content)

                    logger.info(f"结构化推理成功 - 尝试次数: {attempt + 1}")
                    return parsed_result  # 返回解析后的结果而不是response对象
                    
                except json.JSONDecodeError as e:
                    logger.warning(f"JSON解析失败 (尝试 {attempt + 1}/{max_retries}): {str(e)}")
                    if attempt == max_retries - 1:
                        raise ValueError(f"多次尝试后仍无法解析JSON: {str(e)}")
                    continue
                except Exception as e:
                    logger.error(f"推理调用失败 (尝试 {attempt + 1}/{max_retries}): {str(e)}")
                    if attempt == max_retries - 1:
                        raise
                    continue
            
        except Exception as e:
            logger.error(f"结构化推理失败: {str(e)}")
            raise
    
    async def simple_chat(
        self,
        messages: List[Dict[str, str]],
        max_tokens: Optional[int] = None
    ) -> str:
        """
        简单的聊天推理
        
        Args:
            messages: 消息列表
            max_tokens: 最大token数
            
        Returns:
            推理结果文本
        """
        try:
            logger.info("开始简单聊天推理")
            
            # 构建消息
            chat_messages = []
            for msg in messages:
                if msg["role"] == "system":
                    chat_messages.append(SystemMessage(content=msg["content"]))
                elif msg["role"] == "user":
                    chat_messages.append(HumanMessage(content=msg["content"]))
                elif msg["role"] == "assistant":
                    chat_messages.append(AIMessage(content=msg["content"]))
            
            # 临时调整max_tokens
            original_max_tokens = self.llm.max_tokens
            if max_tokens:
                self.llm.max_tokens = max_tokens
            
            try:
                response = await self.llm.ainvoke(chat_messages)
                result = response.content
                
                logger.info("简单聊天推理完成")
                return result
                
            finally:
                # 恢复原始max_tokens
                self.llm.max_tokens = original_max_tokens
            
        except Exception as e:
            logger.error(f"简单聊天推理失败: {str(e)}")
            raise
    
    async def batch_analyze(
        self,
        batch_requests: List[Dict[str, Any]],
        max_concurrent: int = 3
    ) -> List[Any]:
        """
        批量分析
        
        Args:
            batch_requests: 批量请求列表
            max_concurrent: 最大并发数
            
        Returns:
            批量分析结果
        """
        import asyncio
        
        try:
            logger.info(f"开始批量分析 - 请求数量: {len(batch_requests)}")
            
            # 创建信号量控制并发
            semaphore = asyncio.Semaphore(max_concurrent)
            
            async def process_single_request(request):
                async with semaphore:
                    if request.get("response_format"):
                        return await self.analyze_with_structured_output(
                            messages=request["messages"],
                            response_format=request["response_format"]
                        )
                    else:
                        return await self.simple_chat(
                            messages=request["messages"],
                            max_tokens=request.get("max_tokens")
                        )
            
            # 并发执行所有请求
            results = await asyncio.gather(
                *[process_single_request(req) for req in batch_requests],
                return_exceptions=True
            )
            
            logger.info("批量分析完成")
            return results
            
        except Exception as e:
            logger.error(f"批量分析失败: {str(e)}")
            raise
    
    def get_token_count(self, text: str) -> int:
        """
        估算文本的token数量
        
        Args:
            text: 输入文本
            
        Returns:
            估算的token数量
        """
        # 简单的token估算（实际应该使用tiktoken）
        return len(text) // 4
    
    def estimate_cost(self, input_tokens: int, output_tokens: int) -> float:
        """
        估算API调用成本
        
        Args:
            input_tokens: 输入token数
            output_tokens: 输出token数
            
        Returns:
            估算成本（美元）
        """
        # GPT-4o-mini的价格（示例）
        input_price_per_1k = 0.00015  # $0.15 per 1K tokens
        output_price_per_1k = 0.0006  # $0.60 per 1K tokens
        
        input_cost = (input_tokens / 1000) * input_price_per_1k
        output_cost = (output_tokens / 1000) * output_price_per_1k
        
        return input_cost + output_cost
