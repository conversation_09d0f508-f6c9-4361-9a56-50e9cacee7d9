#!/usr/bin/env python3
"""
测试数据库连接和用户画像服务
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.services.user_profile_database_service import get_user_profile_database_service
from src.database.mysql_client import test_connection, fetch_one

async def test_mysql_connection():
    """测试MySQL数据库连接"""
    print("🔗 测试MySQL数据库连接...")
    
    try:
        # 测试基本连接
        is_connected = await test_connection()
        if is_connected:
            print("✅ MySQL数据库连接成功")
        else:
            print("❌ MySQL数据库连接失败")
            return False
        
        # 测试查询用户数据
        print("\n📊 测试查询用户数据...")
        
        # 查询用户摘要
        summary_query = """
        SELECT user_id, summary, keywords 
        FROM dh_user_profile.user_summaries 
        WHERE user_id = 1
        """
        user_summary = await fetch_one(summary_query)
        
        if user_summary:
            print(f"✅ 用户摘要查询成功")
            print(f"   用户ID: {user_summary['user_id']}")
            print(f"   摘要: {user_summary['summary'][:100]}...")
            print(f"   关键词: {user_summary['keywords']}")
        else:
            print("⚠️  未找到用户摘要数据")
        
        # 查询用户记忆
        memory_query = """
        SELECT COUNT(*) as count 
        FROM dh_user_profile.user_memories 
        WHERE user_id = 1
        """
        memory_count = await fetch_one(memory_query)
        
        if memory_count:
            print(f"✅ 用户记忆查询成功，共 {memory_count['count']} 条记忆")
        else:
            print("⚠️  未找到用户记忆数据")
        
        # 查询旅行历史
        history_query = """
        SELECT COUNT(*) as count 
        FROM dh_tripplanner.itineraries 
        WHERE user_id = 1
        """
        history_count = await fetch_one(history_query)
        
        if history_count:
            print(f"✅ 旅行历史查询成功，共 {history_count['count']} 条历史")
        else:
            print("⚠️  未找到旅行历史数据")
        
        # 查询旅行偏好
        preference_query = """
        SELECT user_id, travel_style, travel_summary 
        FROM dh_tripplanner.user_travel_profiles 
        WHERE user_id = 1
        """
        travel_preference = await fetch_one(preference_query)
        
        if travel_preference:
            print(f"✅ 旅行偏好查询成功")
            print(f"   旅行风格: {travel_preference['travel_style']}")
            print(f"   旅行总结: {travel_preference['travel_summary'][:100]}...")
        else:
            print("⚠️  未找到旅行偏好数据")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库连接测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def test_user_profile_service():
    """测试用户画像服务"""
    print("\n🧪 测试用户画像服务...")
    
    try:
        service = get_user_profile_database_service()
        
        # 测试获取综合用户画像
        user_id = 1
        comprehensive_profile = await service.get_user_comprehensive_profile(user_id)
        
        print(f"✅ 用户 {user_id} 综合画像获取成功")
        print(f"   画像完整度: {comprehensive_profile.get('profile_completeness', 0):.2f}")
        print(f"   用户摘要: {'有' if comprehensive_profile.get('user_summary') else '无'}")
        print(f"   用户记忆: {len(comprehensive_profile.get('user_memories', []))} 条")
        print(f"   旅行历史: {len(comprehensive_profile.get('travel_history', []))} 条")
        
        # 测试获取旅行偏好
        travel_preferences = await service.get_user_travel_preferences(user_id)
        
        if travel_preferences:
            print(f"✅ 用户 {user_id} 旅行偏好获取成功")
            print(f"   旅行风格: {travel_preferences.get('travel_style')}")
            print(f"   住宿偏好: {travel_preferences.get('accommodation_pref')}")
            print(f"   交通偏好: {travel_preferences.get('transportation_pref')}")
        else:
            print(f"⚠️  用户 {user_id} 旅行偏好获取失败")
        
        # 测试格式化用户画像
        formatted_profile = service.format_user_profile_for_analysis(comprehensive_profile)
        print(f"✅ 用户画像格式化成功")
        print(f"   格式化文本长度: {len(formatted_profile)} 字符")
        print(f"   格式化文本预览: {formatted_profile[:200]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ 用户画像服务测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主函数"""
    print("🚀 启动数据库连接和用户画像服务测试...")
    print("=" * 60)
    
    # 测试数据库连接
    db_success = await test_mysql_connection()
    
    if not db_success:
        print("\n💥 数据库连接测试失败，无法继续")
        return False
    
    # 测试用户画像服务
    service_success = await test_user_profile_service()
    
    if not service_success:
        print("\n💥 用户画像服务测试失败")
        return False
    
    print("\n🎉 所有测试通过！")
    print("✅ MySQL数据库连接正常")
    print("✅ 用户画像数据查询正常")
    print("✅ 用户画像服务功能正常")
    print("✅ 数据库驱动的用户画像功能已准备就绪")
    
    return True

if __name__ == "__main__":
    success = asyncio.run(main())
    
    if success:
        print("\n🎯 测试结论: 数据库驱动的用户画像功能已成功实现！")
        sys.exit(0)
    else:
        print("\n💥 测试结论: 数据库连接或服务存在问题")
        sys.exit(1)
