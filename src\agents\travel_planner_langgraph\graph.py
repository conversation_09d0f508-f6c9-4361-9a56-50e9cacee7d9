"""
TravelPlannerAgent LangGraph工作流图

使用LangGraph构建的旅行规划Agent工作流，专注于全自动模式实现。
支持双模运行（精准续航规划 vs 通用驾驶辅助）和完整的状态管理。
交互模式预留钩子接口，可在后续扩展。
"""

import logging
import time
from typing import Dict, Any, List, Optional, Callable
from datetime import datetime

from langgraph.graph import StateGraph, END
from langgraph.checkpoint.memory import MemorySaver

from .state import TravelPlanState, ProcessingStage, PlanningMode, create_initial_state, add_event_to_state
from .nodes import (
    core_intent_analyzer_node,
    multi_city_strategy_node,
    driving_context_analyzer_node,
    preference_analyzer_node,
    should_analyze_multi_city,
    should_analyze_driving_context,
    has_error
)

logger = logging.getLogger(__name__)


class TravelPlannerGraph:
    """
    旅行规划Agent图形类

    专注于全自动模式的工作流实现，支持：
    1. 完全自动化的旅行规划流程
    2. 双模运行（精准续航规划 vs 通用驾驶辅助）
    3. 状态管理和事件流
    4. 交互模式钩子预留
    """

    def __init__(self, enable_interaction_hooks: bool = False):
        """
        初始化图形

        Args:
            enable_interaction_hooks: 是否启用交互模式钩子（预留功能）
        """
        self.graph = None
        self.compiled_graph = None
        self.checkpointer = MemorySaver()
        self.enable_interaction_hooks = enable_interaction_hooks
        self.interaction_callbacks = {}  # 交互模式回调钩子
        self._build_graph()
    
    def _build_graph(self):
        """构建全自动模式的工作流图"""
        logger.info("开始构建TravelPlannerAgent全自动工作流图")

        # 创建状态图
        workflow = StateGraph(TravelPlanState)

        # 添加路由节点
        workflow.add_node("router", self._router_node)

        # 添加核心节点（全自动模式）
        workflow.add_node("core_intent_analyzer", self._wrap_with_hooks(core_intent_analyzer_node, "intent_analysis"))
        workflow.add_node("multi_city_strategy", self._wrap_with_hooks(multi_city_strategy_node, "multi_city"))
        workflow.add_node("driving_context_analyzer", self._wrap_with_hooks(driving_context_analyzer_node, "driving_context"))
        workflow.add_node("preference_analyzer", self._wrap_with_hooks(preference_analyzer_node, "preference_analysis"))
        workflow.add_node("itinerary_generator", self._wrap_with_hooks(self._itinerary_generator_node, "itinerary_generation"))
        workflow.add_node("optimizer", self._wrap_with_hooks(self._optimizer_node, "optimization"))
        workflow.add_node("error_handler", self._error_handler_node)

        # 交互模式钩子节点（预留）
        if self.enable_interaction_hooks:
            workflow.add_node("user_confirmation", self._user_confirmation_node)
            workflow.add_node("user_feedback", self._user_feedback_node)

        # 设置入口点为路由节点
        workflow.set_entry_point("router")

        # 构建全自动流程的条件边
        self._add_automatic_flow_edges(workflow)

        # 如果启用交互钩子，添加交互边
        if self.enable_interaction_hooks:
            self._add_interaction_flow_edges(workflow)

        # 编译图形
        self.graph = workflow
        self.compiled_graph = workflow.compile(checkpointer=self.checkpointer)

        logger.info("TravelPlannerAgent全自动工作流图构建完成")

    def _add_automatic_flow_edges(self, workflow):
        """添加全自动流程的边"""

        # 路由节点 -> 分析阶段或规划阶段
        workflow.add_conditional_edges(
            "router",
            self._should_skip_analysis,
            {
                "analysis": "core_intent_analyzer",
                "planning": "itinerary_generator"
            }
        )

        # 核心意图分析 -> 多城市策略或驾驶情境
        workflow.add_conditional_edges(
            "core_intent_analyzer",
            should_analyze_multi_city,
            {
                "multi_city_strategy": "multi_city_strategy",
                "driving_context": "driving_context_analyzer"
            }
        )

        # 多城市策略 -> 驾驶情境或偏好分析
        workflow.add_conditional_edges(
            "multi_city_strategy",
            should_analyze_driving_context,
            {
                "driving_context": "driving_context_analyzer",
                "preference_analysis": "preference_analyzer"
            }
        )

        # 驾驶情境 -> 偏好分析或错误处理
        workflow.add_conditional_edges(
            "driving_context_analyzer",
            has_error,
            {
                "error_handler": "error_handler",
                "continue": "preference_analyzer"
            }
        )

        # 偏好分析 -> 行程生成或错误处理
        workflow.add_conditional_edges(
            "preference_analyzer",
            has_error,
            {
                "error_handler": "error_handler",
                "continue": "itinerary_generator"
            }
        )

        # 行程生成 -> 优化或错误处理
        workflow.add_conditional_edges(
            "itinerary_generator",
            has_error,
            {
                "error_handler": "error_handler",
                "continue": "optimizer"
            }
        )

        # 优化 -> 结束或错误处理
        workflow.add_conditional_edges(
            "optimizer",
            has_error,
            {
                "error_handler": "error_handler",
                "continue": END
            }
        )

        # 错误处理直接结束
        workflow.add_edge("error_handler", END)

    def _add_interaction_flow_edges(self, workflow):
        """添加交互模式的边（预留功能）"""
        # 这里可以在未来添加交互模式的边
        # 例如：在关键节点后添加用户确认步骤
        pass

    def _wrap_with_hooks(self, node_func, hook_name: str):
        """
        为节点函数包装交互钩子（预留功能）

        Args:
            node_func: 原始节点函数
            hook_name: 钩子名称

        Returns:
            包装后的节点函数
        """
        async def wrapped_node(state: TravelPlanState) -> TravelPlanState:
            # 执行前钩子
            if self.enable_interaction_hooks and f"before_{hook_name}" in self.interaction_callbacks:
                callback = self.interaction_callbacks[f"before_{hook_name}"]
                state = await callback(state)

            # 执行原始节点
            state = await node_func(state)

            # 执行后钩子
            if self.enable_interaction_hooks and f"after_{hook_name}" in self.interaction_callbacks:
                callback = self.interaction_callbacks[f"after_{hook_name}"]
                state = await callback(state)

            return state

        return wrapped_node

    # ==================== 交互模式预留节点 ====================

    async def _user_confirmation_node(self, state: TravelPlanState) -> TravelPlanState:
        """
        用户确认节点（预留功能）

        在关键决策点等待用户确认，支持未来的交互模式扩展。
        """
        logger.info(f"用户确认节点 - Session: {state['session_id']} (预留功能)")

        # 添加确认等待事件
        state = add_event_to_state(state, "user_confirmation_required", {
            "stage": state.get("current_stage"),
            "message": "等待用户确认...",
            "confirmation_type": "general"
        })

        # 在全自动模式下，直接通过确认
        if not self.enable_interaction_hooks:
            state = add_event_to_state(state, "auto_confirmed", {
                "message": "全自动模式，自动确认"
            })

        return state

    async def _user_feedback_node(self, state: TravelPlanState) -> TravelPlanState:
        """
        用户反馈节点（预留功能）

        收集用户反馈并调整规划，支持未来的交互模式扩展。
        """
        logger.info(f"用户反馈节点 - Session: {state['session_id']} (预留功能)")

        # 添加反馈收集事件
        state = add_event_to_state(state, "user_feedback_requested", {
            "stage": state.get("current_stage"),
            "message": "请提供您的反馈...",
            "feedback_type": "general"
        })

        # 在全自动模式下，跳过反馈收集
        if not self.enable_interaction_hooks:
            state = add_event_to_state(state, "feedback_skipped", {
                "message": "全自动模式，跳过反馈收集"
            })

        return state

    # ==================== 钩子管理方法 ====================

    def register_interaction_callback(self, hook_name: str, callback):
        """
        注册交互模式回调函数（预留功能）

        Args:
            hook_name: 钩子名称（如 "before_intent_analysis", "after_itinerary_generation"）
            callback: 回调函数
        """
        self.interaction_callbacks[hook_name] = callback
        logger.info(f"注册交互回调: {hook_name}")

    def unregister_interaction_callback(self, hook_name: str):
        """
        取消注册交互模式回调函数

        Args:
            hook_name: 钩子名称
        """
        if hook_name in self.interaction_callbacks:
            del self.interaction_callbacks[hook_name]
            logger.info(f"取消注册交互回调: {hook_name}")

    def enable_interaction_mode(self):
        """启用交互模式（预留功能）"""
        self.enable_interaction_hooks = True
        logger.info("交互模式已启用")

    def disable_interaction_mode(self):
        """禁用交互模式，切换到全自动模式"""
        self.enable_interaction_hooks = False
        logger.info("切换到全自动模式")
    
    async def _itinerary_generator_node(self, state: TravelPlanState) -> TravelPlanState:
        """行程生成节点"""
        logger.info(f"开始行程生成 - Session: {state['session_id']}")
        
        try:
            # 添加开始事件
            state = add_event_to_state(state, "stage_start", {
                "stage": "itinerary_generation",
                "message": "正在为您生成详细的旅行行程..."
            })
            
            # 导入分析服务
            from ..services.analysis_service import AnalysisService
            analysis_service = AnalysisService()
            
            # 生成行程
            itinerary = await analysis_service.generate_itinerary(
                core_intent=state.get("core_intent", {}),
                preference_profile=state.get("preference_profile", {}),
                multi_city_strategy=state.get("multi_city_strategy"),
                driving_context=state.get("driving_context"),
                planning_mode=state.get("planning_mode", "general_assistance")
            )
            
            # 解析每日行程
            daily_itineraries = itinerary.get("daily_itineraries", [])
            state["daily_itineraries"] = daily_itineraries
            
            # 保存行程摘要
            state["summary"] = itinerary.get("itinerary_summary", {})
            
            # 保存推荐信息
            state["recommendations"] = itinerary.get("recommendations", {})
            
            # 记录工具调用
            state["tool_calls"].append({
                "node": "itinerary_generator",
                "timestamp": datetime.now().isoformat(),
                "input": {
                    "planning_mode": state.get("planning_mode"),
                    "destinations": state.get("core_intent", {}).get("destinations"),
                    "days": state.get("core_intent", {}).get("days")
                },
                "output": {
                    "daily_count": len(daily_itineraries),
                    "total_attractions": sum(
                        len([item for item in day.get("items", []) if item.get("type") == "attraction"])
                        for day in daily_itineraries
                    )
                }
            })
            
            # 添加完成事件
            state = add_event_to_state(state, "itinerary_generated", {
                "daily_count": len(daily_itineraries),
                "summary": state["summary"]
            })
            
            logger.info(f"行程生成完成 - 生成了 {len(daily_itineraries)} 天的行程")
            
        except Exception as e:
            logger.error(f"行程生成失败: {str(e)}")
            state["has_error"] = True
            state["error_message"] = f"行程生成失败: {str(e)}"
            state = add_event_to_state(state, "error", {
                "stage": "itinerary_generation",
                "error": str(e)
            })
        
        return state
    
    async def _optimizer_node(self, state: TravelPlanState) -> TravelPlanState:
        """优化节点"""
        logger.info(f"开始行程优化 - Session: {state['session_id']}")
        
        try:
            # 添加开始事件
            state = add_event_to_state(state, "stage_start", {
                "stage": "optimization",
                "message": "正在优化您的行程安排..."
            })
            
            # 这里可以添加行程优化逻辑
            # 例如：路线优化、时间调整、成本优化等
            
            # 标记为完成
            state["is_completed"] = True
            state["current_stage"] = ProcessingStage.COMPLETED.value
            state["updated_at"] = datetime.now().isoformat()
            
            # 添加完成事件
            state = add_event_to_state(state, "planning_completed", {
                "message": "旅行规划已完成！",
                "summary": state.get("summary", {}),
                "total_days": len(state.get("daily_itineraries", []))
            })
            
            logger.info(f"行程优化完成 - Session: {state['session_id']}")
            
        except Exception as e:
            logger.error(f"行程优化失败: {str(e)}")
            state["has_error"] = True
            state["error_message"] = f"行程优化失败: {str(e)}"
            state = add_event_to_state(state, "error", {
                "stage": "optimization",
                "error": str(e)
            })
        
        return state

    def _router_node(self, state: TravelPlanState) -> TravelPlanState:
        """路由节点 - 决定是否跳过分析阶段"""
        logger.info(f"路由节点 - Session: {state['session_id']}")
        logger.info(f"路由节点 - analysis_completed: {state.get('analysis_completed', False)}")
        logger.info(f"路由节点 - current_stage: {state.get('current_stage', 'unknown')}")

        # 如果已经有分析结果，直接跳到规划阶段
        if state.get("analysis_completed", False):
            logger.info("检测到分析已完成，跳转到规划阶段")
            state = add_event_to_state(state, "routing_decision", {
                "decision": "skip_analysis",
                "message": "基于已有分析结果开始规划"
            })
        else:
            logger.info("开始完整的分析流程")
            state = add_event_to_state(state, "routing_decision", {
                "decision": "start_analysis",
                "message": "开始分析用户需求"
            })

        return state

    def _should_skip_analysis(self, state: TravelPlanState) -> str:
        """判断是否跳过分析阶段"""
        if state.get("analysis_completed", False):
            return "planning"
        else:
            return "analysis"

    async def _error_handler_node(self, state: TravelPlanState) -> TravelPlanState:
        """错误处理节点"""
        logger.error(f"进入错误处理 - Session: {state['session_id']}")
        
        # 设置错误状态
        state["current_stage"] = ProcessingStage.ERROR.value
        state["is_completed"] = True
        state["updated_at"] = datetime.now().isoformat()
        
        # 添加错误事件
        state = add_event_to_state(state, "planning_failed", {
            "error": state.get("error_message", "未知错误"),
            "stage": state.get("current_stage", "unknown")
        })
        
        return state
    
    async def run_automatic(
        self,
        user_id: str,
        original_query: str,
        user_profile: Optional[Dict[str, Any]] = None,
        vehicle_info: Optional[Dict[str, Any]] = None,
        session_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        运行全自动旅行规划工作流

        这是主要的运行模式，完全自动化处理用户的旅行规划请求，
        无需用户交互，直接生成完整的旅行方案。

        Args:
            user_id: 用户ID
            original_query: 原始查询
            user_profile: 用户画像
            vehicle_info: 车辆信息
            session_id: 会话ID（可选）

        Returns:
            完整的规划结果
        """
        try:
            # 生成会话ID
            if not session_id:
                session_id = f"auto_plan_{user_id}_{int(datetime.now().timestamp())}"

            logger.info(f"开始运行全自动旅行规划工作流 - Session: {session_id}")

            # 确保处于全自动模式
            original_interaction_mode = self.enable_interaction_hooks
            self.enable_interaction_hooks = False

            try:
                # 创建初始状态
                initial_state = create_initial_state(
                    session_id=session_id,
                    user_id=user_id,
                    original_query=original_query,
                    user_profile=user_profile,
                    vehicle_info=vehicle_info
                )

                # 添加全自动模式标记
                initial_state = add_event_to_state(initial_state, "automatic_mode_start", {
                    "message": "启动全自动规划模式",
                    "mode": "automatic"
                })

                # 运行工作流
                config = {"configurable": {"thread_id": session_id}}

                final_state = await self.compiled_graph.ainvoke(
                    initial_state,
                    config=config
                )

                logger.info(f"全自动旅行规划工作流完成 - Session: {session_id}")
                return final_state

            finally:
                # 恢复原始交互模式设置
                self.enable_interaction_hooks = original_interaction_mode

        except Exception as e:
            logger.error(f"全自动旅行规划工作流失败 - Session: {session_id}, 错误: {str(e)}")
            raise

    # 保持向后兼容的别名
    async def run(self, *args, **kwargs) -> Dict[str, Any]:
        """运行工作流（默认全自动模式）"""
        return await self.run_automatic(*args, **kwargs)
    
    async def stream_run_automatic(
        self,
        user_id: str,
        original_query: str,
        user_profile: Optional[Dict[str, Any]] = None,
        vehicle_info: Optional[Dict[str, Any]] = None,
        session_id: Optional[str] = None
    ):
        """
        流式运行全自动旅行规划工作流

        提供实时的规划进度更新，适合前端SSE事件流展示。
        完全自动化处理，无需用户交互。

        Args:
            user_id: 用户ID
            original_query: 原始查询
            user_profile: 用户画像
            vehicle_info: 车辆信息
            session_id: 会话ID（可选）

        Yields:
            状态更新流
        """
        try:
            # 生成会话ID
            if not session_id:
                session_id = f"auto_stream_{user_id}_{int(datetime.now().timestamp())}"

            logger.info(f"开始流式运行全自动旅行规划工作流 - Session: {session_id}")

            # 确保处于全自动模式
            original_interaction_mode = self.enable_interaction_hooks
            self.enable_interaction_hooks = False

            try:
                # 创建初始状态
                initial_state = create_initial_state(
                    session_id=session_id,
                    user_id=user_id,
                    original_query=original_query,
                    user_profile=user_profile,
                    vehicle_info=vehicle_info
                )

                # 添加全自动流式模式标记
                initial_state = add_event_to_state(initial_state, "automatic_stream_start", {
                    "message": "启动全自动流式规划模式",
                    "mode": "automatic_stream"
                })

                # 流式运行工作流
                config = {"configurable": {"thread_id": session_id}}

                async for chunk in self.compiled_graph.astream(
                    initial_state,
                    config=config
                ):
                    yield chunk

                logger.info(f"流式全自动旅行规划工作流完成 - Session: {session_id}")

            finally:
                # 恢复原始交互模式设置
                self.enable_interaction_hooks = original_interaction_mode

        except Exception as e:
            logger.error(f"流式全自动旅行规划工作流失败 - Session: {session_id}, 错误: {str(e)}")
            raise

    async def stream_run_planning_phase(
        self,
        session_id: str,
        user_id: str,
        original_query: str,
        core_intent: Optional[Dict[str, Any]] = None,
        user_profile: Optional[Dict[str, Any]] = None,
        user_memories: Optional[List[Dict[str, Any]]] = None,
        travel_preferences: Optional[Dict[str, Any]] = None,
        preference_profile: Optional[Dict[str, Any]] = None,
        driving_context: Optional[Dict[str, Any]] = None,
        vehicle_info: Optional[Dict[str, Any]] = None,
        multi_city_strategy: Optional[Dict[str, Any]] = None
    ):
        """
        流式运行规划阶段 - 基于分析结果直接开始规划

        跳过意图分析和偏好分析，直接使用分析阶段的结果进行规划。

        Args:
            session_id: 会话ID
            user_id: 用户ID
            original_query: 原始查询
            core_intent: 核心意图分析结果
            user_profile: 用户画像
            user_memories: 用户记忆
            travel_preferences: 旅行偏好
            preference_profile: 偏好画像
            driving_context: 驾驶情境
            vehicle_info: 车辆信息
            multi_city_strategy: 多城市策略

        Yields:
            状态更新流
        """
        try:
            logger.info(f"开始流式运行规划阶段 - Session: {session_id}")

            # 确保处于全自动模式
            original_interaction_mode = self.enable_interaction_hooks
            self.enable_interaction_hooks = False

            try:
                # 创建带有分析结果的初始状态
                initial_state = create_initial_state(
                    session_id=session_id,
                    user_id=user_id,
                    original_query=original_query,
                    user_profile=user_profile,
                    vehicle_info=vehicle_info
                )

                # 填入分析阶段的结果，这样工作流就不需要重新分析
                initial_state["core_intent"] = core_intent
                initial_state["user_memories"] = user_memories or []
                initial_state["travel_preferences"] = travel_preferences
                initial_state["preference_profile"] = preference_profile
                initial_state["driving_context"] = driving_context
                initial_state["multi_city_strategy"] = multi_city_strategy

                # 设置分析完成标记，让工作流直接跳到规划阶段
                initial_state["analysis_completed"] = True
                initial_state["current_stage"] = "itinerary_generation"

                # 调试日志
                logger.info(f"设置规划阶段初始状态 - analysis_completed: {initial_state.get('analysis_completed')}")
                logger.info(f"核心意图: {core_intent is not None}")
                logger.info(f"用户画像: {user_profile is not None}")
                logger.info(f"旅行偏好: {travel_preferences is not None}")

                # 添加规划阶段开始标记
                initial_state = add_event_to_state(initial_state, "planning_started", {
                    "message": "基于分析结果开始规划",
                    "mode": "planning_phase"
                })

                # 流式运行工作流
                config = {"configurable": {"thread_id": session_id}}

                # 使用现有的全自动工作流
                async for chunk in self.compiled_graph.astream(
                    initial_state,
                    config=config
                ):
                    yield chunk

                logger.info(f"流式规划阶段完成 - Session: {session_id}")

            finally:
                # 恢复原始交互模式设置
                self.enable_interaction_hooks = original_interaction_mode

        except Exception as e:
            logger.error(f"流式规划阶段失败 - Session: {session_id}, 错误: {str(e)}")
            raise

    # 保持向后兼容的别名
    async def stream_run(self, *args, **kwargs):
        """流式运行工作流（默认全自动模式）"""
        async for chunk in self.stream_run_automatic(*args, **kwargs):
            yield chunk

    # ==================== 交互模式预留方法 ====================

    async def run_interactive(
        self,
        user_id: str,
        original_query: str,
        user_profile: Optional[Dict[str, Any]] = None,
        vehicle_info: Optional[Dict[str, Any]] = None,
        session_id: Optional[str] = None,
        interaction_config: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        运行交互式旅行规划工作流（预留功能）

        支持在关键决策点与用户交互，收集反馈并调整规划。
        目前为预留接口，可在后续版本中实现。

        Args:
            user_id: 用户ID
            original_query: 原始查询
            user_profile: 用户画像
            vehicle_info: 车辆信息
            session_id: 会话ID（可选）
            interaction_config: 交互配置

        Returns:
            交互式规划结果
        """
        logger.info(f"交互式规划模式（预留功能） - 用户: {user_id}")

        # 目前回退到全自动模式
        logger.warning("交互式模式尚未实现，回退到全自动模式")
        return await self.run_automatic(
            user_id=user_id,
            original_query=original_query,
            user_profile=user_profile,
            vehicle_info=vehicle_info,
            session_id=session_id
        )

    async def stream_run_analysis_only(
        self,
        user_id: str,
        original_query: str,
        user_profile: Optional[Dict[str, Any]] = None,
        vehicle_info: Optional[Dict[str, Any]] = None,
        session_id: Optional[str] = None
    ):
        """
        流式运行分析阶段

        只执行意图分析和偏好分析，不进行POI查询和行程生成。

        Args:
            user_id: 用户ID
            original_query: 原始查询
            user_profile: 用户画像
            vehicle_info: 车辆信息
            session_id: 会话ID（可选）

        Yields:
            分析阶段状态更新流
        """
        if session_id is None:
            session_id = f"analysis_{int(time.time())}"

        logger.info(f"开始分析阶段流式执行 - Session: {session_id}")

        try:
            # 创建初始状态
            initial_state = create_initial_state(
                session_id=session_id,
                user_id=user_id,
                original_query=original_query,
                user_profile=user_profile,
                vehicle_info=vehicle_info
            )

            # 手动执行分析节点
            from .nodes import core_intent_analyzer_node, preference_analyzer_node

            # 执行核心意图分析
            state_after_intent = await core_intent_analyzer_node(initial_state)
            yield {"core_intent_analyzer": state_after_intent}

            # 执行偏好分析
            state_after_preference = await preference_analyzer_node(state_after_intent)
            yield {"preference_analyzer": state_after_preference}

            # 发送分析完成事件，包含完整的分析结果
            final_analysis_state = {
                "analysis_completed": True,
                "session_id": session_id,
                "user_id": user_id,
                "query": original_query,
                "core_intent": state_after_preference.get("core_intent"),
                "user_profile": state_after_preference.get("user_profile"),
                "user_memories": state_after_preference.get("user_memories"),
                "travel_preferences": state_after_preference.get("travel_preferences"),
                "preference_profile": state_after_preference.get("preference_profile"),
                "driving_context": state_after_preference.get("driving_context"),
                "vehicle_info": state_after_preference.get("vehicle_info"),
                "multi_city_strategy": state_after_preference.get("multi_city_strategy")
            }
            yield {"analysis_complete": final_analysis_state}

        except Exception as e:
            logger.error(f"分析阶段流式执行失败 - Session: {session_id}, 错误: {str(e)}")
            raise

    async def stream_run_interactive(
        self,
        user_id: str,
        original_query: str,
        user_profile: Optional[Dict[str, Any]] = None,
        vehicle_info: Optional[Dict[str, Any]] = None,
        session_id: Optional[str] = None,
        interaction_config: Optional[Dict[str, Any]] = None
    ):
        """
        流式运行交互式旅行规划工作流（预留功能）

        支持实时交互和反馈收集的流式规划。
        目前为预留接口，可在后续版本中实现。

        Args:
            user_id: 用户ID
            original_query: 原始查询
            user_profile: 用户画像
            vehicle_info: 车辆信息
            session_id: 会话ID（可选）
            interaction_config: 交互配置

        Yields:
            交互式状态更新流
        """
        logger.info(f"交互式流式规划模式（预留功能） - 用户: {user_id}")

        # 目前回退到全自动流式模式
        logger.warning("交互式流式模式尚未实现，回退到全自动流式模式")
        async for chunk in self.stream_run_automatic(
            user_id=user_id,
            original_query=original_query,
            user_profile=user_profile,
            vehicle_info=vehicle_info,
            session_id=session_id
        ):
            yield chunk
    
    async def get_state_history(self, session_id: str) -> List[Dict[str, Any]]:
        """
        获取状态历史
        
        Args:
            session_id: 会话ID
            
        Returns:
            状态历史列表
        """
        try:
            config = {"configurable": {"thread_id": session_id}}
            history = []
            
            async for state in self.compiled_graph.aget_state_history(config):
                history.append(state.values)
            
            return history
            
        except Exception as e:
            logger.error(f"获取状态历史失败 - Session: {session_id}, 错误: {str(e)}")
            return []
    
    def get_graph_visualization(self, include_interaction_hooks: bool = False) -> str:
        """
        获取图形可视化

        Args:
            include_interaction_hooks: 是否包含交互模式钩子（预留功能）

        Returns:
            Mermaid格式的图形定义
        """
        if include_interaction_hooks:
            # 包含交互钩子的完整图形（预留）
            mermaid_graph = """
            graph TD
                A[core_intent_analyzer] --> A1{交互钩子?}
                A1 -->|否| B{多城市?}
                A1 -->|是| UC1[user_confirmation]
                UC1 --> B
                B -->|是| C[multi_city_strategy]
                B -->|否| D[driving_context_analyzer]
                C --> C1{交互钩子?}
                C1 -->|否| E{自驾?}
                C1 -->|是| UC2[user_confirmation]
                UC2 --> E
                E -->|是| D
                E -->|否| F[preference_analyzer]
                D --> G{有错误?}
                G -->|是| H[error_handler]
                G -->|否| F
                F --> F1{交互钩子?}
                F1 -->|否| J[itinerary_generator]
                F1 -->|是| UF[user_feedback]
                UF --> J
                J --> K{有错误?}
                K -->|是| H
                K -->|否| L[optimizer]
                L --> M{有错误?}
                M -->|是| H
                M -->|否| N[END]
                H --> N

                style UC1 fill:#e1f5fe
                style UC2 fill:#e1f5fe
                style UF fill:#f3e5f5
                style A fill:#e8f5e8
                style J fill:#fff3e0
                style L fill:#fce4ec
            """
        else:
            # 全自动模式的简洁图形
            mermaid_graph = """
            graph TD
                A[核心意图分析] --> B{多城市?}
                B -->|是| C[多城市策略]
                B -->|否| D[驾驶情境分析]
                C --> E{自驾?}
                E -->|是| D
                E -->|否| F[偏好分析]
                D --> G{有错误?}
                G -->|是| H[错误处理]
                G -->|否| F
                F --> I{有错误?}
                I -->|是| H
                I -->|否| J[行程生成]
                J --> K{有错误?}
                K -->|是| H
                K -->|否| L[行程优化]
                L --> M{有错误?}
                M -->|是| H
                M -->|否| N[完成]
                H --> N

                style A fill:#e8f5e8
                style C fill:#e3f2fd
                style D fill:#fff8e1
                style F fill:#f1f8e9
                style J fill:#fff3e0
                style L fill:#fce4ec
                style H fill:#ffebee
                style N fill:#e0f2f1

                classDef autoNode fill:#e8f5e8,stroke:#4caf50,stroke-width:2px
                class A,C,D,F,J,L autoNode
            """

        return mermaid_graph.strip()
