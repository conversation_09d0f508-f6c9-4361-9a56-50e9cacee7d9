"""
MySQL数据库CRUD操作
基于新建的mysql_models提供完整的数据库操作接口
复用itinerary目录下的CRUD设计模式

注意：这些CRUD操作是为Pydantic模型设计的，不直接执行数据库操作，
而是提供模型验证和数据转换功能。实际的数据库操作需要SQLAlchemy ORM。
"""
from typing import List, Optional, Dict, Any, TypeVar, Generic, Type
from sqlalchemy.orm import Session
from sqlalchemy import func, and_, or_, text, select
from datetime import datetime, date
import json

from .mysql_models import DhTripPlanner, DhUserProfile
from .user.user_schemas import UserMemory, User, UserSummary
from .itinerary.itinerary_schemas import Itinerary, AIPlanningSession as AIPlanningSessionORM

ModelType = TypeVar("ModelType")

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm.attributes import flag_modified

class AsyncCRUDBase(Generic[ModelType]):
    def __init__(self, model: Type[ModelType]):
        self.model = model

    async def get(self, db: AsyncSession, id: int) -> Optional[ModelType]:
        result = await db.execute(select(self.model).where(self.model.id == id))
        return result.scalar_one_or_none()

    async def get_multi(self, db: AsyncSession, *, skip: int = 0, limit: int = 100) -> List[ModelType]:
        result = await db.execute(select(self.model).offset(skip).limit(limit))
        return result.scalars().all()

    async def create(self, db: AsyncSession, *, obj_in: dict) -> ModelType:
        db_obj = self.model(**obj_in)
        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)
        return db_obj

    async def update(self, db: AsyncSession, *, db_obj: ModelType, obj_in: dict) -> ModelType:
        for field, value in obj_in.items():
            setattr(db_obj, field, value)
        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)
        return db_obj

    async def delete(self, db: AsyncSession, *, id: int) -> ModelType:
        result = await db.execute(select(self.model).where(self.model.id == id))
        obj = result.scalar_one_or_none()
        if obj:
            await db.delete(obj)
            await db.commit()
        return obj

class CRUDBase(Generic[ModelType]):
    """基础CRUD操作类 - 为Pydantic模型提供验证和转换功能"""
    
    def __init__(self, model: Type[ModelType]):
        self.model = model

    def get(self, db: Session, id: Any) -> Optional[ModelType]:
        """根据ID获取单条记录（模拟实现）"""
        # 注意：这是模拟实现，实际需要SQLAlchemy ORM
        return None

    def get_multi(self, db: Session, *, skip: int = 0, limit: int = 100) -> List[ModelType]:
        """获取多条记录，支持分页（模拟实现）"""
        # 注意：这是模拟实现，实际需要SQLAlchemy ORM
        return []

    def create(self, db: Session, *, obj_in: Dict[str, Any]) -> ModelType:
        """创建新记录（模拟实现）"""
        # 验证数据并创建Pydantic模型实例
        validated_data = {k: v for k, v in obj_in.items() if v is not None}
        return self.model(**validated_data)

    def update(self, db: Session, *, db_obj: ModelType, obj_in: Dict[str, Any]) -> ModelType:
        """更新记录（模拟实现）"""
        # 更新模型数据
        update_data = {k: v for k, v in obj_in.items() if v is not None}
        updated_data = db_obj.model_dump()
        updated_data.update(update_data)
        return self.model(**updated_data)

    def delete(self, db: Session, *, id: Any) -> Optional[ModelType]:
        """删除记录（模拟实现）"""
        # 注意：这是模拟实现，实际需要SQLAlchemy ORM
        return None

    def validate_data(self, data: Dict[str, Any]) -> ModelType:
        """验证并转换数据为Pydantic模型"""
        return self.model(**data)


# =================================================================
# dh_tripplanner 数据库CRUD操作（模拟实现）
# =================================================================

class CRUDAIPlanningSession(AsyncCRUDBase[AIPlanningSessionORM]):
    """AI规划会话CRUD操作"""

    async def create_session(self, db: AsyncSession, *, session_data: Dict[str, Any]) -> AIPlanningSessionORM:
        """创建并验证AI规划会话"""
        session_data.setdefault('created_at', datetime.utcnow())
        session_data.setdefault('status', 'PENDING')
        
        db_obj = self.model(**session_data)
        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)
        return db_obj

    async def update_session(self, db: AsyncSession, *, id: str, session_data: Dict[str, Any]) -> Optional[AIPlanningSessionORM]:
        """更新AI规划会话"""
        print(id)
        result = await db.execute(
            select(self.model).where(self.model.id == id)
        )
        print(result)
        db_obj = result.scalar_one_or_none()
        if db_obj:
            for key, value in session_data.items():
                print(f"key={key}, value={value}, type={type(value)}")
                setattr(db_obj, key, value)
            
            if "raw_llm_output" in session_data:
                flag_modified(db_obj, "raw_llm_output")
            
            db.add(db_obj)
            await db.commit()
            await db.refresh(db_obj)
        return db_obj

    async def get_by_user(self, db: AsyncSession, *, user_id: int, skip: int = 0, limit: int = 100) -> List[AIPlanningSessionORM]:
        """获取用户的所有规划会话"""
        result = await db.execute(
            select(self.model)
            .where(self.model.user_id == user_id)
            .offset(skip)
            .limit(limit)
        )
        return result.scalars().all()

    async def update_status(self, db: AsyncSession, *, session_id: str, status: str,
                            planning_log: Optional[str] = None,
                            raw_llm_output: Optional[Dict[str, Any]] = None,
                            final_itinerary_id: Optional[int] = None) -> Optional[AIPlanningSessionORM]:
        """更新会话状态"""
        result = await db.execute(select(self.model).where(self.model.session_id == session_id))
        session = result.scalar_one_or_none()
        if session:
            session.status = status
            session.completed_at = datetime.utcnow()
            if planning_log is not None:
                session.planning_log = planning_log
            if raw_llm_output is not None:
                session.raw_llm_output = raw_llm_output
            if final_itinerary_id is not None:
                session.final_itinerary_id = final_itinerary_id
            
            db.add(session)
            await db.commit()
            await db.refresh(session)
        return session


class CRUDItinerary(AsyncCRUDBase):
    """行程CRUD操作 (SQLAlchemy ORM实现)"""
    def __init__(self, model):
        super().__init__(model)

    async def get_by_user(self, db: AsyncSession, *, user_id: int, skip: int = 0, limit: int = 100) -> list:
        result = await db.execute(
            select(self.model).where(self.model.user_id == user_id).offset(skip).limit(limit)
        )
        return result.scalars().all()

    async def get_templates(self, db: AsyncSession, *, skip: int = 0, limit: int = 100) -> list:
        result = await db.execute(
            select(self.model).where(self.model.is_template == True).offset(skip).limit(limit)
        )
        return result.scalars().all()

    async def search_by_city(self, db: AsyncSession, *, city_name: str, skip: int = 0, limit: int = 100) -> list:
        result = await db.execute(
            select(self.model).where(self.model.city_name == city_name).offset(skip).limit(limit)
        )
        return result.scalars().all()

    async def get_with_days_and_pois(self, db: AsyncSession, *, itinerary_id: int) -> dict:
        # 这里只返回主行程对象，具体天和POI需业务层补充
        result = await db.execute(
            select(self.model).where(self.model.id == itinerary_id)
        )
        itinerary = result.scalar_one_or_none()
        return itinerary

    async def create_itinerary(self, db: AsyncSession, *, itinerary_data: dict):
        itinerary_data.setdefault('created_at', datetime.now())
        itinerary_data.setdefault('updated_at', datetime.now())
        itinerary_data.setdefault('status_id', 1)
        itinerary_data.setdefault('is_template', False)
        db_obj = self.model(**itinerary_data)
        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)
        return db_obj


class CRUDPOI(CRUDBase):
    """POI CRUD操作"""
    
    def search(self, db: Session, *, keyword: str, type_id: Optional[int] = None, 
              skip: int = 0, limit: int = 100) -> List[Any]:
        """搜索POI（模拟实现）"""
        return []
    
    def get_nearby(self, db: Session, *, latitude: float, longitude: float, 
                   radius_km: float = 5.0, type_id: Optional[int] = None,
                   skip: int = 0, limit: int = 100) -> List[Any]:
        """获取附近的POI（模拟实现）"""
        return []

    def create_poi(self, *, poi_data: Dict[str, Any]) -> DhTripPlanner.POI:
        """创建并验证POI"""
        return self.validate_data(poi_data)


class CRUDUserTripStats(CRUDBase):
    """用户行程统计CRUD操作"""
    
    def get_by_user(self, db: Session, *, user_id: int) -> Optional[Any]:
        """获取用户行程统计（模拟实现）"""
        return None
    
    def update_stats(self, db: Session, *, user_id: int, 
                    trip_count_delta: int = 0,
                    city_count_delta: int = 0, 
                    mileage_delta: float = 0.0,
                    days_delta: int = 0) -> Any:
        """更新用户统计数据（模拟实现）"""
        return None


# =================================================================
# dh_user_profile 数据库CRUD操作（模拟实现）
# =================================================================

class CRUDUser(AsyncCRUDBase):
    """用户CRUD操作"""

    def get_by_nickname(self, db: Session, *, nickname: str) -> Optional[Any]:
        """根据昵称获取用户（模拟实现）"""
        return None
    
    def get_active_users(self, db: Session, *, skip: int = 0, limit: int = 100) -> List[Any]:
        """获取活跃用户列表（模拟实现）"""
        return []
    
    def update_status(self, db: Session, *, user_id: int, status: str) -> Optional[Any]:
        """更新用户状态（模拟实现）"""
        return None

    def create_user(self, *, user_data: Dict[str, Any]) -> DhUserProfile.User:
        """创建并验证用户"""
        # 添加默认值
        user_data.setdefault('created_at', datetime.now())
        user_data.setdefault('updated_at', datetime.now())
        user_data.setdefault('status', 'ACTIVE')
        return self.validate_data(user_data)


class CRUDUserMemory(AsyncCRUDBase):
    """用户记忆CRUD操作 (SQLAlchemy ORM实现)"""
    def __init__(self, model):
        super().__init__(model)

    async def get_by_user(self, db: AsyncSession, *, user_id: int, skip: int = 0, limit: int = 100) -> List[UserMemory]:
        result = await db.execute(
            select(self.model).where(self.model.user_id == user_id).offset(skip).limit(limit)
        )
        return result.scalars().all()

    async def search_memories(self, db: AsyncSession, *, user_id: int, keyword: str, min_confidence: float = 0.0) -> List[UserMemory]:
        result = await db.execute(
            select(self.model).where(
                self.model.user_id == user_id,
                self.model.memory_content.contains(keyword),
                self.model.confidence >= min_confidence
            )
        )
        return result.scalars().all()

    async def update_last_accessed(self, db: AsyncSession, *, memory_id: int) -> Optional[UserMemory]:
        result = await db.execute(
            select(self.model).where(self.model.id == memory_id)
        )
        memory = result.scalar_one_or_none()
        if memory:
            memory.last_accessed = datetime.now()
            db.add(memory)
            await db.commit()
            await db.refresh(memory)
        return memory

    async def get_high_confidence_memories(self, db: AsyncSession, *, user_id: int, min_confidence: float = 0.8, limit: int = 50) -> List[UserMemory]:
        result = await db.execute(
            select(self.model)
            .where(
                self.model.user_id == user_id,
                self.model.confidence >= min_confidence
            )
            .order_by(self.model.confidence.desc())
            .limit(limit)
        )
        return result.scalars().all()

    async def create_memory(self, db: AsyncSession, *, memory_data: Dict[str, Any]) -> UserMemory:
        memory_data.setdefault('created_at', datetime.now())
        memory_data.setdefault('confidence', 1.0)
        db_obj = self.model(**memory_data)
        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)
        return db_obj


class CRUDUserSummary(AsyncCRUDBase):
    """用户画像CRUD操作 (SQLAlchemy ORM实现)"""
    def __init__(self, model):
        super().__init__(model)

    async def get_by_user(self, db: AsyncSession, *, user_id: int) -> Optional[UserSummary]:
        result = await db.execute(
            select(self.model).where(self.model.user_id == user_id)
        )
        return result.scalar_one_or_none()

    async def update_summary(self, db: AsyncSession, *, user_id: int, summary_data: dict) -> Optional[UserSummary]:
        result = await db.execute(
            select(self.model).where(self.model.user_id == user_id)
        )
        user_summary = result.scalar_one_or_none()
        if user_summary:
            for key, value in summary_data.items():
                setattr(user_summary, key, value)
                if key == "keywords":
                    flag_modified(user_summary, "keywords")
            db.add(user_summary)
            await db.commit()
            await db.refresh(user_summary)
        return user_summary

    async def create_summary(self, db: AsyncSession, *, summary_data: dict) -> UserSummary:
        summary_data.setdefault('updated_at', datetime.now())
        summary_data.setdefault('keywords', [])
        db_obj = self.model(**summary_data)
        db.add(db_obj)
        await db.commit()
        await db.refresh(db_obj)
        return db_obj


class CRUDCredential(CRUDBase):
    """用户凭证CRUD操作"""
    
    def get_by_provider_identifier(self, db: Session, *, provider: str, identifier: str) -> Optional[Any]:
        """根据提供方和标识获取凭证（模拟实现）"""
        return None
    
    def get_by_user_provider(self, db: Session, *, user_id: int, provider: str) -> Optional[Any]:
        """根据用户和提供方获取凭证（模拟实现）"""
        return None

    def create_credential(self, *, credential_data: Dict[str, Any]) -> DhUserProfile.Credential:
        """创建并验证用户凭证"""
        # 添加默认值
        credential_data.setdefault('created_at', datetime.now())
        credential_data.setdefault('updated_at', datetime.now())
        return self.validate_data(credential_data)


# =================================================================
# CRUD实例化对象
# =================================================================

# dh_tripplanner 数据库CRUD实例
ai_planning_session_crud = CRUDAIPlanningSession(AIPlanningSessionORM)
itinerary_crud = CRUDItinerary(Itinerary)
itinerary_day_crud = CRUDBase(DhTripPlanner.ItineraryDay)
itinerary_day_poi_crud = CRUDBase(DhTripPlanner.ItineraryDayPOI)
poi_crud = CRUDPOI(DhTripPlanner.POI)
poi_type_crud = CRUDBase(DhTripPlanner.POIType)
status_definition_crud = CRUDBase(DhTripPlanner.StatusDefinition)
tag_crud = CRUDBase(DhTripPlanner.Tag)
user_trip_stats_crud = CRUDUserTripStats(DhTripPlanner.UserTripStats)
user_travel_profile_crud = CRUDBase(DhTripPlanner.UserTravelProfile)
user_app_settings_crud = CRUDBase(DhTripPlanner.UserAppSettings)
user_media_crud = CRUDBase(DhTripPlanner.UserMedia)
ai_vlog_crud = CRUDBase(DhTripPlanner.AIVlog)
vlog_media_item_crud = CRUDBase(DhTripPlanner.VlogMediaItem)

# dh_user_profile 数据库CRUD实例
user_crud = CRUDUser(User)
vehicle_crud = CRUDBase(DhUserProfile.Vehicle)
credential_crud = CRUDCredential(DhUserProfile.Credential)
user_memory_crud = CRUDUserMemory(UserMemory)
user_summary_crud = CRUDUserSummary(UserSummary)
user_vehicle_binding_crud = CRUDBase(DhUserProfile.UserVehicleBinding)
favorite_tag_crud = CRUDBase(DhUserProfile.FavoriteTag)
user_favorite_crud = CRUDBase(DhUserProfile.UserFavorite)

# 导出所有CRUD类和实例
__all__ = [
    # 基础类
    "CRUDBase",
    
    # dh_tripplanner CRUD类
    "CRUDAIPlanningSession", "CRUDItinerary", "CRUDPOI", "CRUDUserTripStats",
    
    # dh_user_profile CRUD类  
    "CRUDUser", "CRUDUserMemory", "CRUDUserSummary", "CRUDCredential",
    
    # dh_tripplanner CRUD实例
    "ai_planning_session_crud", "itinerary_crud", "itinerary_day_crud", 
    "itinerary_day_poi_crud", "poi_crud", "poi_type_crud", "status_definition_crud", 
    "tag_crud", "user_trip_stats_crud", "user_travel_profile_crud", 
    "user_app_settings_crud", "user_media_crud", "ai_vlog_crud", "vlog_media_item_crud",
    
    # dh_user_profile CRUD实例
    "user_crud", "vehicle_crud", "credential_crud", "user_memory_crud", 
    "user_summary_crud", "user_vehicle_binding_crud", "favorite_tag_crud", 
    "user_favorite_crud"
] 