"""
数据库服务模块

提供用户画像和旅行历史的数据库查询功能
"""

import logging
import asyncio
from typing import Dict, Any, List, Optional
import pymysql
from pymysql.cursors import DictCursor

from src.core.config import get_settings

logger = logging.getLogger(__name__)


class DatabaseService:
    """数据库服务类，处理用户画像相关的数据库查询"""
    
    def __init__(self):
        self.settings = get_settings()
        self._connection_pool = {}
    
    def _get_connection(self, database: str):
        """获取数据库连接"""
        if database not in self._connection_pool:
            try:
                connection = pymysql.connect(
                    host=self.settings.MYSQL_HOST,
                    user=self.settings.MYSQL_USER,
                    password=self.settings.MYSQL_PASSWORD,
                    database=database,
                    charset='utf8mb4',
                    cursorclass=DictCursor,
                    autocommit=True
                )
                self._connection_pool[database] = connection
                logger.info(f"成功连接到数据库: {database}")
            except Exception as e:
                logger.error(f"连接数据库失败 {database}: {str(e)}")
                raise
        
        return self._connection_pool[database]
    
    async def get_user_travel_profile(self, user_id: int) -> Optional[Dict[str, Any]]:
        """
        获取用户旅行画像
        
        Args:
            user_id: 用户ID
            
        Returns:
            用户旅行画像数据
        """
        try:
            connection = self._get_connection('dh_tripplanner')
            
            with connection.cursor() as cursor:
                sql = """
                SELECT user_id, travel_style, accommodation_pref, transportation_pref, 
                       travel_summary, travel_keywords, updated_at
                FROM user_travel_profiles 
                WHERE user_id = %s
                """
                cursor.execute(sql, (user_id,))
                result = cursor.fetchone()
                
                if result:
                    logger.info(f"成功获取用户 {user_id} 的旅行画像")
                    return result
                else:
                    logger.warning(f"未找到用户 {user_id} 的旅行画像")
                    return None
                    
        except Exception as e:
            logger.error(f"查询用户旅行画像失败 - 用户ID: {user_id}, 错误: {str(e)}")
            return None
    
    async def get_user_memories(self, user_id: int, limit: int = 10) -> List[Dict[str, Any]]:
        """
        获取用户记忆
        
        Args:
            user_id: 用户ID
            limit: 返回记录数限制
            
        Returns:
            用户记忆列表
        """
        try:
            connection = self._get_connection('dh_user_profile')
            
            with connection.cursor() as cursor:
                sql = """
                SELECT id, user_id, memory_content, source_session_id, 
                       confidence, created_at, last_accessed
                FROM user_memories 
                WHERE user_id = %s 
                ORDER BY created_at DESC 
                LIMIT %s
                """
                cursor.execute(sql, (user_id, limit))
                results = cursor.fetchall()
                
                logger.info(f"成功获取用户 {user_id} 的 {len(results)} 条记忆")
                return results
                
        except Exception as e:
            logger.error(f"查询用户记忆失败 - 用户ID: {user_id}, 错误: {str(e)}")
            return []
    
    async def get_user_summary(self, user_id: int) -> Optional[Dict[str, Any]]:
        """
        获取用户画像摘要
        
        Args:
            user_id: 用户ID
            
        Returns:
            用户画像摘要数据
        """
        try:
            connection = self._get_connection('dh_user_profile')
            
            with connection.cursor() as cursor:
                sql = """
                SELECT user_id, summary, keywords, model_version, updated_at
                FROM user_summaries 
                WHERE user_id = %s
                """
                cursor.execute(sql, (user_id,))
                result = cursor.fetchone()
                
                if result:
                    logger.info(f"成功获取用户 {user_id} 的画像摘要")
                    return result
                else:
                    logger.warning(f"未找到用户 {user_id} 的画像摘要")
                    return None
                    
        except Exception as e:
            logger.error(f"查询用户画像摘要失败 - 用户ID: {user_id}, 错误: {str(e)}")
            return None
    
    async def get_user_travel_history(self, user_id: int, limit: int = 5) -> List[Dict[str, Any]]:
        """
        获取用户旅行历史
        
        Args:
            user_id: 用户ID
            limit: 返回记录数限制
            
        Returns:
            用户旅行历史列表
        """
        try:
            connection = self._get_connection('dh_tripplanner')
            
            with connection.cursor() as cursor:
                sql = """
                SELECT id, user_id, title, city_name, total_days, start_date, 
                       total_distance, notes, created_at
                FROM itineraries 
                WHERE user_id = %s 
                ORDER BY created_at DESC 
                LIMIT %s
                """
                cursor.execute(sql, (user_id, limit))
                results = cursor.fetchall()
                
                logger.info(f"成功获取用户 {user_id} 的 {len(results)} 条旅行历史")
                return results
                
        except Exception as e:
            logger.error(f"查询用户旅行历史失败 - 用户ID: {user_id}, 错误: {str(e)}")
            return []
    
    async def get_comprehensive_user_profile(self, user_id: int) -> Dict[str, Any]:
        """
        获取用户的综合画像数据
        
        Args:
            user_id: 用户ID
            
        Returns:
            包含所有用户画像信息的综合数据
        """
        logger.info(f"开始获取用户 {user_id} 的综合画像数据")
        
        # 并发查询所有相关数据
        travel_profile_task = self.get_user_travel_profile(user_id)
        memories_task = self.get_user_memories(user_id, limit=10)
        summary_task = self.get_user_summary(user_id)
        history_task = self.get_user_travel_history(user_id, limit=5)
        
        travel_profile, memories, summary, history = await asyncio.gather(
            travel_profile_task, memories_task, summary_task, history_task,
            return_exceptions=True
        )
        
        # 构建综合画像
        comprehensive_profile = {
            "user_id": user_id,
            "travel_profile": travel_profile if not isinstance(travel_profile, Exception) else None,
            "memories": memories if not isinstance(memories, Exception) else [],
            "summary": summary if not isinstance(summary, Exception) else None,
            "travel_history": history if not isinstance(history, Exception) else [],
            "profile_completeness": 0.0
        }
        
        # 计算画像完整度
        completeness_score = 0.0
        if comprehensive_profile["travel_profile"]:
            completeness_score += 0.3
        if comprehensive_profile["memories"]:
            completeness_score += 0.3
        if comprehensive_profile["summary"]:
            completeness_score += 0.2
        if comprehensive_profile["travel_history"]:
            completeness_score += 0.2
            
        comprehensive_profile["profile_completeness"] = completeness_score
        
        logger.info(f"用户 {user_id} 综合画像获取完成，完整度: {completeness_score:.2f}")
        
        return comprehensive_profile
    
    def close_connections(self):
        """关闭所有数据库连接"""
        for database, connection in self._connection_pool.items():
            try:
                connection.close()
                logger.info(f"已关闭数据库连接: {database}")
            except Exception as e:
                logger.error(f"关闭数据库连接失败 {database}: {str(e)}")
        
        self._connection_pool.clear()


# 全局数据库服务实例
_database_service = None

def get_database_service() -> DatabaseService:
    """获取数据库服务实例"""
    global _database_service
    if _database_service is None:
        _database_service = DatabaseService()
    return _database_service
