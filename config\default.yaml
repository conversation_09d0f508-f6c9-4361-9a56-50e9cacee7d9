# AutoPilot AI Default Configuration
# 此文件包含所有非敏感的默认配置
# 敏感信息（如API Key）应在 .env 文件中配置

# Application Settings
app:
  name: "AutoPilot AI"
  version: "0.1.0"
  environment: "development"

# API Settings
api:
  host: "0.0.0.0"
  port: 8000
  reload: true
  docs_url: "/docs"
  redoc_url: "/redoc"

# LLM Model Names (API Keys 在 .env 中配置)
reasoning_llm:
  model: "glm-z1-flash"  # 智谱AI思考模型
  base_url: "https://open.bigmodel.cn/api/paas/v4/"
  # api_key 通过环境变量 REASONING_LLM_API_KEY 加载
  api_key: "d1d80fa7b613e862659f12a821478914.4k7HpUSfpfyjpvvW"
basic_llm:
  model: "glm-4-flash"  # 智谱AI基础模型（示例）
  base_url: "https://open.bigmodel.cn/api/paas/v4/"
  api_key: "d1d80fa7b613e862659f12a821478914.4k7HpUSfpfyjpvvW"
  # api_key 通过环境变量 BASIC_LLM_API_KEY 加载
map_llm:
  model: "glm-4-flash"  # 智谱AI地图模型
  base_url: "https://open.bigmodel.cn/api/paas/v4/"
  api_key: "d1d80fa7b613e862659f12a821478914.4k7HpUSfpfyjpvvW"
  # api_key 通过环境变量 MAP_LLM_API_KEY 加载

# MySQL Settings
mysql:
  host: "***********"
  port: 3306
  user: "root"
  password: "Fsit#2024"
  database: "dh_tripplanner"
  # 可以通过环境变量 MY_SQL_* 覆盖各项配置


# MongoDB Settings
mongodb:
  host: "***********"
  port: 27017
  username: "admin"
  password: "m25uids*g"
  database: "dh_platform_data"

# Redis Settings
redis:
  host: "***********"
  port: 5182
  password: "kLKe3NFM4RZMgXhA"
  expiry_time: 86400  # 24小时过期（秒）
  # 可以通过环境变量 REDIS_* 覆盖各项配置

# Logging Settings
logging:
  level: "INFO"
  format: "json"
  
# Memory Settings
memory:
  # L1 短期记忆 TTL (秒)
  short_term_ttl: 3600
  # L2 长期记忆批量大小
  long_term_batch_size: 50

# Prompt Settings
prompts:
  template_dir: "src/prompts/templates"
  auto_reload: true

minio:
  endpoint: "***********:9000"
  access_key: "admin"
  secret_key: "Fsti#2024"
  bucket: "aivlog"
  secure: false 

comfyui_url: "http://************:8193"