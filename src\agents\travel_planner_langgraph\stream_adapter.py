"""
SSE流适配器 (Stream Adapter)

将LangGraph State快照转换为SSE事件流，确保与前端接口100%兼容。
"""

import json
import logging
from typing import Dict, Any, List, Optional, AsyncGenerator, Union
from datetime import datetime

from .state import TravelPlanState, ProcessingStage

logger = logging.getLogger(__name__)


class SSEStreamAdapter:
    """SSE流适配器类"""
    
    def __init__(self):
        """初始化SSE流适配器"""
        self.event_id_counter = 0
    
    async def convert_langgraph_stream_to_sse(
        self,
        langgraph_stream: AsyncGenerator[Dict[str, Any], None],
        session_id: str
    ) -> AsyncGenerator[str, None]:
        """
        将LangGraph流转换为SSE事件流
        
        Args:
            langgraph_stream: LangGraph状态流
            session_id: 会话ID
            
        Yields:
            SSE格式的事件字符串
        """
        try:
            logger.info(f"开始转换LangGraph流为SSE - Session: {session_id}")
            
            # 发送开始事件
            yield self._format_sse_event(
                event_type="stream_start",
                data={
                    "session_id": session_id,
                    "timestamp": datetime.now().isoformat(),
                    "message": "开始处理您的旅行规划请求..."
                }
            )
            
            async for chunk in langgraph_stream:
                # 处理每个状态块
                for node_name, state_update in chunk.items():
                    if isinstance(state_update, dict):
                        # 转换状态更新为SSE事件
                        sse_events = self._convert_state_to_sse_events(
                            node_name, 
                            state_update, 
                            session_id
                        )
                        
                        for event in sse_events:
                            yield event
            
            # 发送结束事件
            yield self._format_sse_event(
                event_type="stream_end",
                data={
                    "session_id": session_id,
                    "timestamp": datetime.now().isoformat(),
                    "message": "处理完成"
                }
            )
            
            logger.info(f"LangGraph流转换完成 - Session: {session_id}")
            
        except Exception as e:
            logger.error(f"LangGraph流转换失败 - Session: {session_id}, 错误: {str(e)}")
            
            # 发送错误事件
            yield self._format_sse_event(
                event_type="error",
                data={
                    "session_id": session_id,
                    "error": str(e),
                    "timestamp": datetime.now().isoformat()
                }
            )
    
    def _convert_state_to_sse_events(
        self,
        node_name: str,
        state: Dict[str, Any],
        session_id: str
    ) -> List[str]:
        """
        将状态更新转换为SSE事件列表
        
        Args:
            node_name: 节点名称
            state: 状态数据
            session_id: 会话ID
            
        Returns:
            SSE事件字符串列表
        """
        events = []
        
        try:
            # 获取当前阶段
            current_stage = state.get("current_stage", "unknown")
            
            # 根据节点类型生成不同的事件
            if node_name == "core_intent_analyzer":
                events.extend(self._handle_intent_analysis_events(state, session_id))
                # 在意图分析完成后，触发规划启动事件
                if state.get("core_intent"):
                    events.extend(self._handle_planning_start_events(state, session_id))
            elif node_name == "multi_city_strategy":
                events.extend(self._handle_multi_city_events(state, session_id))
            elif node_name == "driving_context_analyzer":
                events.extend(self._handle_driving_context_events(state, session_id))
                # 在驾驶上下文分析后，触发天气查询事件
                if state.get("driving_context"):
                    events.extend(self._handle_weather_events(state, session_id))
            elif node_name == "preference_analyzer":
                events.extend(self._handle_preference_events(state, session_id))
                # 在偏好分析完成后，触发POI搜索事件
                if state.get("preference_profile"):
                    events.extend(self._handle_poi_search_events(state, session_id))
            elif node_name == "analysis_complete":
                # 处理分析完成事件，发送包含完整分析结果的complete事件
                events.extend(self._handle_analysis_complete_events(state, session_id))
            elif node_name == "itinerary_generator":
                # 在行程生成阶段，触发完整的规划流程事件
                events.extend(self._handle_itinerary_events(state, session_id))
            elif node_name == "optimizer":
                events.extend(self._handle_optimization_events(state, session_id))
            elif node_name == "error_handler":
                events.extend(self._handle_error_events(state, session_id))
            
            # 处理状态中的事件列表
            if "events" in state and state["events"] is not None:
                for event in state["events"]:
                    if event is not None and isinstance(event, dict):
                        try:
                            events.append(self._format_sse_event(
                                event_type=event.get("type", "update"),
                                data={
                                    "session_id": session_id,
                                    "node": node_name,
                                    "stage": current_stage,
                                    **event.get("data", {})
                                },
                                event_id=event.get("id")
                            ))
                        except Exception as event_error:
                            logger.warning(f"处理事件失败: {event_error}, 事件: {event}")
            
        except Exception as e:
            logger.error(f"状态转换失败 - 节点: {node_name}, 错误: {str(e)}")
            events.append(self._format_sse_event(
                event_type="conversion_error",
                data={
                    "session_id": session_id,
                    "node": node_name,
                    "error": str(e)
                }
            ))
        
        return events
    
    def _handle_intent_analysis_events(
        self,
        state: Dict[str, Any],
        session_id: str
    ) -> List[str]:
        """处理意图分析事件"""
        events = []

        # 阶段开始事件
        events.append(self._format_sse_event(
            event_type="stage_progress",
            data={
                "session_id": session_id,
                "stage": "intent_analysis",
                "progress": 10,
                "message": "正在分析您的旅行意图..."
            }
        ))

        # 添加用户画像解析的流式输出
        user_profile = state.get("user_profile") or {}
        user_memories = state.get("user_memories") or []

        if user_profile or user_memories:
            # 发送用户画像解析进度事件
            events.append(self._format_sse_event(
                event_type="stage_progress",
                data={
                    "session_id": session_id,
                    "stage": "user_profile_analysis",
                    "progress": 15,
                    "message": "正在解析您的用户画像和历史偏好..."
                }
            ))

            # 构建用户画像分析内容
            profile_analysis_parts = []

            # 从用户画像中提取信息
            if user_profile:
                travel_profile = user_profile.get("travel_profile", {})
                if travel_profile:
                    travel_style = travel_profile.get("travel_style")
                    if travel_style:
                        style_map = {
                            "ADVENTURE": "探险型",
                            "RELAXED": "休闲型",
                            "FAMILY": "家庭型",
                            "BUDGET": "经济型",
                            "LUXURY": "豪华型",
                            "BUSINESS": "商务型"
                        }
                        profile_analysis_parts.append(f"旅行风格：{style_map.get(travel_style, travel_style)}")

                budget_preference = user_profile.get("budget_preference")
                if budget_preference:
                    profile_analysis_parts.append(f"预算偏好：{budget_preference}")

            # 从用户记忆中提取关键信息
            memory_insights = []
            for memory in user_memories[:5]:  # 只取前5条记忆
                content = memory.get("content", "")
                if len(content) > 50:
                    content = content[:50] + "..."
                memory_insights.append(content)

            if memory_insights:
                profile_analysis_parts.append(f"历史偏好：{len(user_memories)}条记忆")

            # 如果有画像信息，发送分析结果
            if profile_analysis_parts:
                profile_content = "、".join(profile_analysis_parts)

                events.append(self._format_sse_event(
                    event_type="thinking_step",
                    data={
                        "session_id": session_id,
                        "category": "其他",
                        "content": f"已获取您的用户画像：{profile_content}。正在结合这些信息分析您的旅行需求..."
                    }
                ))

        # 如果有核心意图结果，发送analysis_step事件
        if "core_intent" in state:
            core_intent = state["core_intent"]

            # 构建用户需求分析内容
            destinations_str = "、".join(core_intent.get("destinations", []))
            days = core_intent.get("days", 0)
            travel_theme = core_intent.get("travel_theme", "")
            travelers = core_intent.get("travelers", {})
            budget = core_intent.get("budget", {})
            transportation = core_intent.get("transportation", {})

            # 构建分析内容文本
            content_parts = []
            if destinations_str and days:
                content_parts.append(f"{destinations_str}|{days}天")
            if travelers.get("composition"):
                content_parts.append(travelers["composition"])
            if travel_theme:
                content_parts.append(travel_theme)
            if budget.get("level"):
                content_parts.append(f"预算：{budget['level']}")
            if transportation.get("primary_mode"):
                mode_map = {"self_driving": "自驾", "public_transport": "公共交通", "flight": "飞机"}
                mode_text = mode_map.get(transportation["primary_mode"], transportation["primary_mode"])
                if transportation.get("distance_km"):
                    content_parts.append(f"续航里程：{transportation['distance_km']}KM")
                else:
                    content_parts.append(mode_text)

            analysis_content = " | ".join(content_parts) if content_parts else "已解析用户基本需求"

            events.append(self._format_sse_event(
                event_type="analysis_step",
                data={
                    "session_id": session_id,
                    "step_type": "user_intent",
                    "title": "解析用户需求和画像",
                    "content": analysis_content,
                    "confidence": core_intent.get("confidence_score", 0),
                    "completed": True
                }
            ))

        return events
    
    def _handle_multi_city_events(
        self, 
        state: Dict[str, Any], 
        session_id: str
    ) -> List[str]:
        """处理多城市策略事件"""
        events = []
        
        events.append(self._format_sse_event(
            event_type="stage_progress",
            data={
                "session_id": session_id,
                "stage": "multi_city_strategy",
                "progress": 30,
                "message": "正在制定多城市旅行策略..."
            }
        ))
        
        if "multi_city_strategy" in state:
            strategy = state["multi_city_strategy"]
            events.append(self._format_sse_event(
                event_type="strategy_planned",
                data={
                    "session_id": session_id,
                    "strategy_type": strategy.get("strategy_type"),
                    "recommended_order": strategy.get("recommended_order", []),
                    "progress": 40
                }
            ))
        
        return events
    
    def _handle_driving_context_events(
        self, 
        state: Dict[str, Any], 
        session_id: str
    ) -> List[str]:
        """处理驾驶情境事件"""
        events = []
        
        events.append(self._format_sse_event(
            event_type="stage_progress",
            data={
                "session_id": session_id,
                "stage": "driving_context",
                "progress": 45,
                "message": "正在分析驾驶需求..."
            }
        ))
        
        if "driving_context" in state:
            context = state["driving_context"]
            events.append(self._format_sse_event(
                event_type="driving_analyzed",
                data={
                    "session_id": session_id,
                    "driving_strategy": context.get("driving_strategy"),
                    "planning_mode": state.get("planning_mode"),
                    "progress": 50
                }
            ))
        
        return events
    
    def _handle_preference_events(
        self,
        state: Dict[str, Any],
        session_id: str
    ) -> List[str]:
        """处理偏好分析事件"""
        events = []

        events.append(self._format_sse_event(
            event_type="stage_progress",
            data={
                "session_id": session_id,
                "stage": "preference_analysis",
                "progress": 60,
                "message": "正在分析您的旅行偏好..."
            }
        ))

        if "preference_profile" in state and state["preference_profile"] is not None:
            profile = state["preference_profile"]

            # 发送景点偏好分析结果
            attraction_prefs = profile.get("attraction_preferences", {})
            if attraction_prefs:
                # 提取景点偏好信息
                attraction_content_parts = []
                if attraction_prefs.get("preferred_types"):
                    types_str = "、".join(attraction_prefs["preferred_types"])
                    attraction_content_parts.append(f"景点推荐：{types_str}")
                else:
                    attraction_content_parts.append("景点推荐：乐园/休闲")

                if attraction_prefs.get("depth_preference"):
                    attraction_content_parts.append(attraction_prefs["depth_preference"])
                else:
                    attraction_content_parts.append("是否深度游、时间节奏")

                attraction_content = " | ".join(attraction_content_parts) if attraction_content_parts else "景点推荐：乐园/休闲/"

                events.append(self._format_sse_event(
                    event_type="analysis_step",
                    data={
                        "session_id": session_id,
                        "step_type": "poi_preference",
                        "title": "景点偏好类型",
                        "content": attraction_content,
                        "confidence": attraction_prefs.get("confidence_score", 0),
                        "completed": True
                    }
                ))

            # 发送美食偏好分析结果
            food_prefs = profile.get("food_preferences", {})
            if food_prefs:
                # 提取美食偏好信息
                food_content_parts = []

                # 处理口味偏好
                taste_prefs = food_prefs.get("taste_preferences", {})
                if taste_prefs:
                    basic_tastes = taste_prefs.get("basic_tastes", {})
                    flavor_profiles = taste_prefs.get("flavor_profiles", {})

                    # 提取高分口味偏好（评分>=7）
                    high_taste_prefs = []
                    for taste, score in basic_tastes.items():
                        try:
                            if int(score) >= 7:
                                taste_names = {
                                    "sweet": "甜味", "sour": "酸味", "spicy": "辣味",
                                    "salty": "咸味", "umami": "鲜味", "bitter": "苦味"
                                }
                                if taste in taste_names:
                                    high_taste_prefs.append(taste_names[taste])
                        except (ValueError, TypeError):
                            continue

                    # 提取高分风味偏好
                    for flavor, score in flavor_profiles.items():
                        try:
                            if int(score) >= 7:
                                flavor_names = {
                                    "spicy_numbing": "麻辣", "light_fresh": "清淡",
                                    "rich_heavy": "浓郁", "seafood_fresh": "海鲜鲜香"
                                }
                                if flavor in flavor_names:
                                    high_taste_prefs.append(flavor_names[flavor])
                        except (ValueError, TypeError):
                            continue

                    if high_taste_prefs:
                        food_content_parts.extend(high_taste_prefs)

                # 处理菜系偏好
                cuisine_prefs = food_prefs.get("cuisine_preferences", {})
                if cuisine_prefs:
                    chinese_cuisines = cuisine_prefs.get("chinese_cuisines", {})
                    high_cuisine_prefs = []

                    for cuisine, score in chinese_cuisines.items():
                        try:
                            if int(score) >= 7:
                                cuisine_names = {
                                    "sichuan": "川菜", "cantonese": "粤菜", "hunan": "湘菜",
                                    "shandong": "鲁菜", "jiangsu": "苏菜", "zhejiang": "浙菜",
                                    "fujian": "闽菜", "anhui": "徽菜"
                                }
                                if cuisine in cuisine_names:
                                    high_cuisine_prefs.append(cuisine_names[cuisine])
                        except (ValueError, TypeError):
                            continue

                    if high_cuisine_prefs:
                        food_content_parts.extend(high_cuisine_prefs)

                # 处理餐厅类型偏好
                restaurant_prefs = food_prefs.get("restaurant_type_preferences", {})
                if restaurant_prefs:
                    high_restaurant_prefs = []

                    for rest_type, score in restaurant_prefs.items():
                        try:
                            if int(score) >= 7:
                                type_names = {
                                    "fine_dining": "高档餐厅", "specialty_themed": "特色餐厅",
                                    "traditional_heritage": "传统老店", "street_food": "街边小吃",
                                    "chain_brands": "连锁品牌", "buffet": "自助餐厅"
                                }
                                if rest_type in type_names:
                                    high_restaurant_prefs.append(type_names[rest_type])
                        except (ValueError, TypeError):
                            continue

                    if high_restaurant_prefs:
                        food_content_parts.extend(high_restaurant_prefs)

                # 处理特殊需求
                special_reqs = food_prefs.get("special_requirements", {})
                if special_reqs:
                    dietary_restrictions = special_reqs.get("dietary_restrictions", [])
                    health_needs = special_reqs.get("health_needs", [])

                    if dietary_restrictions:
                        food_content_parts.extend(dietary_restrictions)
                    if health_needs:
                        food_content_parts.extend(health_needs)

                # 如果没有具体偏好信息，使用默认内容
                if not food_content_parts:
                    food_content = "不辣、口碑老店、干净、性价比高"
                else:
                    food_content = "、".join(food_content_parts)

                events.append(self._format_sse_event(
                    event_type="analysis_step",
                    data={
                        "session_id": session_id,
                        "step_type": "food_preference",
                        "title": "美食偏好",
                        "content": food_content,
                        "confidence": food_prefs.get("confidence_score", 0),
                        "completed": True
                    }
                ))

            # 添加住宿偏好分析（基于真实用户数据）
            core_intent = state.get("core_intent", {})
            budget = core_intent.get("budget", {})
            travelers = core_intent.get("travelers", {})

            # 从用户画像中获取住宿偏好
            user_profile = state.get("user_profile") or {}
            user_memories = state.get("user_memories") or []

            # 构建住宿偏好内容
            accommodation_content_parts = []

            # 1. 从用户旅行画像中获取住宿偏好
            # 从state中直接获取旅行偏好数据
            travel_preferences = state.get("travel_preferences")
            if travel_preferences and travel_preferences.get("accommodation_pref"):
                try:
                    import json
                    accommodation_pref_data = travel_preferences["accommodation_pref"]
                    if isinstance(accommodation_pref_data, str):
                        accommodation_prefs = json.loads(accommodation_pref_data)
                    else:
                        accommodation_prefs = accommodation_pref_data

                    if isinstance(accommodation_prefs, list):
                        accommodation_content_parts.extend(accommodation_prefs)
                except (json.JSONDecodeError, TypeError):
                    pass

            # 也尝试从user_profile中获取
            travel_profile = user_profile.get("travel_profile")
            if travel_profile and travel_profile.get("accommodation_pref"):
                try:
                    import json
                    if isinstance(travel_profile["accommodation_pref"], str):
                        accommodation_prefs = json.loads(travel_profile["accommodation_pref"])
                    else:
                        accommodation_prefs = travel_profile["accommodation_pref"]

                    if isinstance(accommodation_prefs, list):
                        accommodation_content_parts.extend(accommodation_prefs)
                except (json.JSONDecodeError, TypeError):
                    pass

            # 2. 从用户记忆中提取住宿相关偏好
            for memory in user_memories:
                memory_content = memory.get("content", "").lower()
                if "住宿" in memory_content or "酒店" in memory_content or "民宿" in memory_content:
                    # 提取预算信息
                    if "预算" in memory_content:
                        if "1500" in memory_content or "1800" in memory_content:
                            accommodation_content_parts.append("中高端1500-1800元")
                        elif "800" in memory_content or "1000" in memory_content:
                            accommodation_content_parts.append("经济型800-1000元")
                        elif "2000" in memory_content or "豪华" in memory_content:
                            accommodation_content_parts.append("豪华型2000元以上")

                    # 提取品牌偏好
                    if "连锁" in memory_content:
                        accommodation_content_parts.append("连锁品牌")
                    if "精品" in memory_content:
                        accommodation_content_parts.append("精品酒店")
                    if "民宿" in memory_content:
                        accommodation_content_parts.append("特色民宿")

            # 3. 根据预算等级设置默认价格范围
            if not any("元" in part for part in accommodation_content_parts):
                budget_level = budget.get("level") or user_profile.get("budget_preference", "中等")
                if budget_level == "经济型" or budget_level == "低":
                    accommodation_content_parts.append("经济型800-1200元")
                elif budget_level == "豪华型" or budget_level == "高":
                    accommodation_content_parts.append("豪华型2000-3000元")
                else:
                    accommodation_content_parts.append("中档1200-1800元")

            # 4. 添加基础需求
            if travelers.get("has_children"):
                accommodation_content_parts.append("儿童友好")

            # 5. 添加自驾相关需求（根据PRD要求）
            accommodation_content_parts.extend(["免费停车", "安静整洁"])

            # 6. 如果没有任何偏好，使用默认设置
            if not accommodation_content_parts:
                accommodation_content_parts = ["中档连锁酒店", "1200-1800元", "免费停车", "安静整洁"]

            accommodation_content = "、".join(accommodation_content_parts)

            events.append(self._format_sse_event(
                event_type="analysis_step",
                data={
                    "session_id": session_id,
                    "step_type": "accommodation_preference",
                    "title": "住宿偏好",
                    "content": accommodation_content,
                    "confidence": 0.8,
                    "completed": True
                }
            ))

        return events

    def _handle_planning_start_events(
        self,
        state: Dict[str, Any],
        session_id: str
    ) -> List[str]:
        """处理规划启动事件 - 用户点击立即规划后触发"""
        events = []

        # 发送规划启动事件
        events.append(self._format_sse_event(
            event_type="planning_started",
            data={
                "session_id": session_id,
                "stage": "planning_initiation",
                "progress": 60,
                "message": "🚀 开始为您规划精彩行程...",
                "destinations": state.get("core_intent", {}).get("destinations", []),
                "days": state.get("core_intent", {}).get("days", 0)
            }
        ))

        # 发送语音播报
        destinations = state.get("core_intent", {}).get("destinations", [])
        days = state.get("core_intent", {}).get("days", 0)

        if destinations:
            dest_str = "、".join(destinations)
            tts_content = f"好的，我现在开始为您规划{dest_str}{days}日游的详细行程。让我先查询一下当地的天气情况和热门景点。"
        else:
            tts_content = "好的，我现在开始为您规划详细的旅行行程。"

        events.append(self._format_sse_event(
            event_type="tts_content",
            data={
                "session_id": session_id,
                "content": tts_content,
                "priority": "normal"
            }
        ))

        return events

    def _handle_weather_events(
        self,
        state: Dict[str, Any],
        session_id: str
    ) -> List[str]:
        """处理天气查询事件"""
        events = []

        events.append(self._format_sse_event(
            event_type="stage_progress",
            data={
                "session_id": session_id,
                "stage": "weather_query",
                "progress": 65,
                "message": "正在查询目的地天气信息..."
            }
        ))

        if "weather_info" in state:
            weather_info = state["weather_info"]
            weather_desc = self._format_weather_description(weather_info)

            events.append(self._format_sse_event(
                event_type="weather_info",
                data={
                    "session_id": session_id,
                    "weather": weather_info,
                    "description": weather_desc,
                    "message": f"天气查询完成：{weather_desc}"
                }
            ))

            # 天气相关的旅行建议
            weather_tips = self._generate_weather_tips(weather_info)
            if weather_tips:
                events.append(self._format_sse_event(
                    event_type="travel_tips",
                    data={
                        "session_id": session_id,
                        "tips": weather_tips,
                        "category": "weather",
                        "message": "根据天气情况为您准备了出行建议"
                    }
                ))

        return events

    def _handle_poi_search_events(
        self,
        state: Dict[str, Any],
        session_id: str
    ) -> List[str]:
        """处理POI搜索事件"""
        events = []

        events.append(self._format_sse_event(
            event_type="stage_progress",
            data={
                "session_id": session_id,
                "stage": "poi_search",
                "progress": 70,
                "message": "正在搜索精选景点和美食..."
            }
        ))

        # 分类搜索进度
        search_categories = ["attractions", "restaurants", "hotels"]
        for i, category in enumerate(search_categories):
            events.append(self._format_sse_event(
                event_type="poi_search_progress",
                data={
                    "session_id": session_id,
                    "category": category,
                    "category_name": self._get_category_name(category),
                    "progress": 70 + (i + 1) * 3,
                    "message": f"正在搜索{self._get_category_name(category)}信息..."
                }
            ))

        # POI搜索结果
        if "poi_results" in state:
            poi_results = state["poi_results"]
            total_pois = sum(len(pois) for pois in poi_results.values())

            events.append(self._format_sse_event(
                event_type="poi_search_completed",
                data={
                    "session_id": session_id,
                    "total_pois": total_pois,
                    "categories": list(poi_results.keys()),
                    "message": f"已找到{total_pois}个优质推荐"
                }
            ))

            # 发送每个类别的POI结果
            for category, pois in poi_results.items():
                if pois:  # 只发送有结果的类别
                    events.append(self._format_sse_event(
                        event_type="poi_category_results",
                        data={
                            "session_id": session_id,
                            "category": category,
                            "category_name": self._get_category_name(category),
                            "pois": self._format_poi_list(pois[:5]),  # 只发送前5个
                            "total_count": len(pois),
                            "message": f"{self._get_category_name(category)}推荐已准备就绪"
                        }
                    ))

        return events

    def _format_weather_description(self, weather_info: Dict[str, Any]) -> str:
        """格式化天气描述"""
        weather = weather_info.get("weather", "晴朗")
        temperature = weather_info.get("temperature", "")

        if temperature:
            return f"{weather}，{temperature}"
        else:
            return weather

    def _generate_weather_tips(self, weather_info: Dict[str, Any]) -> List[str]:
        """根据天气生成旅行建议"""
        tips = []
        weather = weather_info.get("weather", "").lower()

        if "雨" in weather:
            tips.extend([
                "建议携带雨具，选择室内景点作为备选",
                "雨天路滑，自驾请注意安全"
            ])
        elif "雪" in weather:
            tips.extend([
                "注意保暖，建议穿着防滑鞋",
                "雪天路况复杂，建议调整行程时间"
            ])
        elif "晴" in weather:
            tips.extend([
                "天气晴朗，适合户外活动",
                "注意防晒，建议携带太阳镜和防晒霜"
            ])

        return tips

    def _handle_itinerary_events(
        self,
        state: Dict[str, Any],
        session_id: str
    ) -> List[str]:
        """处理行程生成事件 - 实现完整的规划阶段流式输出"""
        events = []

        # 阶段B.1: 规划核心POI
        events.extend(self._handle_poi_planning_events(state, session_id))

        # 阶段B.2: 路线优化与充电规划
        events.extend(self._handle_route_optimization_events(state, session_id))

        # 阶段B.3: 生成最终行程
        events.extend(self._handle_final_itinerary_generation_events(state, session_id))

        return events

    def _handle_poi_planning_events(
        self,
        state: Dict[str, Any],
        session_id: str
    ) -> List[str]:
        """处理POI规划事件"""
        events = []

        # 发送POI规划开始事件
        events.append(self._format_sse_event(
            event_type="stage_progress",
            data={
                "session_id": session_id,
                "stage": "poi_planning",
                "progress": 70,
                "message": "正在查询目的地POI信息..."
            }
        ))

        # 天气信息查询
        if "weather_info" in state and state["weather_info"] is not None:
            weather_info = state["weather_info"]
            events.append(self._format_sse_event(
                event_type="weather_info",
                data={
                    "session_id": session_id,
                    "weather": weather_info,
                    "message": f"天气情况：{weather_info.get('weather', '晴朗')}，温度{weather_info.get('temperature', '适宜')}"
                }
            ))

        # POI查询进度
        destinations = state.get("core_intent", {}).get("destinations", [])
        for i, destination in enumerate(destinations):
            events.append(self._format_sse_event(
                event_type="poi_search_progress",
                data={
                    "session_id": session_id,
                    "destination": destination,
                    "progress": 70 + (i + 1) * 5,
                    "message": f"正在查询{destination}的景点、餐厅和住宿信息..."
                }
            ))

        # POI搜索结果
        if "poi_results" in state:
            poi_results = state["poi_results"]
            for category, pois in poi_results.items():
                events.append(self._format_sse_event(
                    event_type="poi_results",
                    data={
                        "session_id": session_id,
                        "category": category,
                        "pois": self._format_poi_list(pois),
                        "count": len(pois),
                        "message": f"找到{len(pois)}个{self._get_category_name(category)}推荐"
                    }
                ))

        return events

    def _handle_route_optimization_events(
        self,
        state: Dict[str, Any],
        session_id: str
    ) -> List[str]:
        """处理路线优化事件"""
        events = []

        events.append(self._format_sse_event(
            event_type="stage_progress",
            data={
                "session_id": session_id,
                "stage": "route_optimization",
                "progress": 85,
                "message": "正在优化行程路线..."
            }
        ))

        # 路线规划进度
        if "route_planning" in state:
            route_info = state["route_planning"]
            events.append(self._format_sse_event(
                event_type="route_planning",
                data={
                    "session_id": session_id,
                    "route_info": route_info,
                    "message": "已完成最优路线规划"
                }
            ))

        # 充电站规划（如果是电动车）
        if state.get("driving_context", {}).get("vehicle_type") == "electric":
            if "charging_plan" in state:
                charging_plan = state["charging_plan"]
                events.append(self._format_sse_event(
                    event_type="charging_plan",
                    data={
                        "session_id": session_id,
                        "charging_stations": charging_plan,
                        "message": f"已规划{len(charging_plan)}个充电点"
                    }
                ))

        return events

    def _handle_final_itinerary_generation_events(
        self,
        state: Dict[str, Any],
        session_id: str
    ) -> List[str]:
        """处理最终行程生成事件"""
        events = []

        events.append(self._format_sse_event(
            event_type="stage_progress",
            data={
                "session_id": session_id,
                "stage": "itinerary_generation",
                "progress": 95,
                "message": "正在生成详细行程安排..."
            }
        ))

        # 生成每日行程卡片 - 检查多种可能的字段名
        daily_itineraries = None
        if "daily_itineraries" in state and state["daily_itineraries"] is not None:
            daily_itineraries = state["daily_itineraries"]
        elif "itinerary" in state and state["itinerary"] is not None:
            # 检查是否有嵌套的daily_itineraries
            itinerary_data = state["itinerary"]
            if isinstance(itinerary_data, dict) and "daily_itineraries" in itinerary_data:
                daily_itineraries = itinerary_data["daily_itineraries"]
            elif isinstance(itinerary_data, list):
                daily_itineraries = itinerary_data

        if daily_itineraries:
            for day_index, day_itinerary in enumerate(daily_itineraries):
                events.append(self._format_sse_event(
                    event_type="daily_itinerary",
                    data={
                        "session_id": session_id,
                        "day": day_index + 1,
                        "itinerary": self._format_daily_itinerary(day_itinerary),
                        "message": f"第{day_index + 1}天行程已生成"
                    }
                ))

            # 发送完整行程摘要
            events.append(self._format_sse_event(
                event_type="itinerary_summary",
                data={
                    "session_id": session_id,
                    "total_days": len(daily_itineraries),
                    "summary": state.get("summary", {}),
                    "highlights": self._extract_itinerary_highlights(daily_itineraries),
                    "progress": 98
                }
            ))

        # 发送规划完成事件
        events.append(self._format_sse_event(
            event_type="planning_completed",
            data={
                "session_id": session_id,
                "result": {
                    "itinerary": daily_itineraries,
                    "summary": state.get("summary", {})
                },
                "summary": state.get("summary", {}),
                "message": "🎉 行程规划完成！"
            }
        ))

        return events

    def _format_poi_list(self, pois: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """格式化POI列表为前端展示格式"""
        formatted_pois = []

        for poi in pois:
            # 安全检查POI数据
            if not poi or not isinstance(poi, dict):
                continue

            formatted_poi = {
                "id": poi.get("id", ""),
                "name": poi.get("name", ""),
                "address": poi.get("address", ""),
                "description": poi.get("description", poi.get("name", "")),
                "images": poi.get("images", []),
                "price": self._format_price_info(poi.get("price")),
                "rating": poi.get("rating", 0),
                "reviews": poi.get("reviews", []),
                "business_hours": poi.get("business_hours", "营业时间请咨询"),
                "parking_info": poi.get("parking_info", "停车信息请咨询"),
                "location": {
                    "latitude": poi.get("location", {}).get("latitude", 0),
                    "longitude": poi.get("location", {}).get("longitude", 0)
                },
                "distance": poi.get("distance"),
                "tags": poi.get("tags", []),
                "phone": poi.get("phone", ""),
                "website": poi.get("website", "")
            }
            formatted_pois.append(formatted_poi)

        return formatted_pois

    def _format_price_info(self, price_data: Any) -> Dict[str, Any]:
        """格式化价格信息"""
        if not price_data:
            return {"type": "unknown", "value": "价格请咨询", "currency": "CNY"}

        if isinstance(price_data, (int, float)):
            return {"type": "fixed", "value": f"¥{price_data}", "currency": "CNY"}

        if isinstance(price_data, dict):
            return {
                "type": price_data.get("type", "unknown"),
                "value": price_data.get("value", "价格请咨询"),
                "currency": price_data.get("currency", "CNY"),
                "range": price_data.get("range", "")
            }

        return {"type": "text", "value": str(price_data), "currency": "CNY"}

    def _get_category_name(self, category: str) -> str:
        """获取POI类别的中文名称"""
        category_names = {
            "attractions": "景点",
            "restaurants": "餐厅",
            "hotels": "住宿",
            "shopping": "购物",
            "entertainment": "娱乐",
            "transport": "交通"
        }
        return category_names.get(category, category)

    def _format_daily_itinerary(self, day_itinerary: Dict[str, Any]) -> Dict[str, Any]:
        """格式化单日行程"""
        # 安全检查输入参数
        if not day_itinerary or not isinstance(day_itinerary, dict):
            logger.warning(f"无效的日行程数据: {day_itinerary}")
            return {
                "day": 1,
                "theme": "",
                "items": [],
                "total_distance": 0,
                "estimated_duration": "",
                "notes": []
            }

        # 处理不同的数据结构
        activities = []

        # 尝试从不同的字段获取活动数据
        if day_itinerary.get("items"):
            activities = day_itinerary.get("items", [])
        elif day_itinerary.get("time_blocks"):
            activities = day_itinerary.get("time_blocks", [])
        elif day_itinerary.get(" timings"):  # 注意前面有空格
            activities = day_itinerary.get(" timings", [])
        elif day_itinerary.get("timings"):
            activities = day_itinerary.get("timings", [])
        elif day_itinerary.get("schedule"):
            activities = day_itinerary.get("schedule", [])
        else:
            # 处理按时段分组的数据结构
            time_periods = ["morning", "lunch", "afternoon", "evening", "dinner", "night",
                          "上午", "午餐", "下午", "晚上", "晚餐", "夜晚"]

            for period in time_periods:
                period_data = day_itinerary.get(period)
                if period_data and isinstance(period_data, dict):
                    # 将时段数据转换为活动格式
                    activity = {
                        "time": period_data.get("time", ""),
                        "location": period_data.get("location", ""),
                        "activity": period_data.get("activity", ""),
                        "activities": period_data.get("activities", []),
                        "description": period_data.get("description", ""),
                        "details": period_data.get("details", ""),
                        "restaurant": period_data.get("restaurant", ""),
                        "cuisine": period_data.get("cuisine", ""),
                        "note": period_data.get("note", ""),
                        "餐饮": period_data.get("餐饮", "")
                    }

                    # 如果是餐饮时段，特殊处理
                    if period in ["lunch", "dinner", "午餐", "晚餐"] or period_data.get("restaurant"):
                        activity["type"] = "dining"
                        activity["location"] = period_data.get("restaurant") or period_data.get("location", "")
                        activity["description"] = period_data.get("cuisine") or period_data.get("note", "")

                    activities.append(activity)

        # 转换活动数据格式
        formatted_items = []
        for activity in activities:
            if activity and isinstance(activity, dict):  # 添加安全检查
                formatted_item = self._convert_activity_to_item(activity)
                if formatted_item:
                    formatted_items.append(formatted_item)

        # 获取天数信息
        day_number = day_itinerary.get("day", 1)
        if isinstance(day_itinerary.get("date"), str):
            date_str = day_itinerary.get("date", "")
            if "Day" in date_str:
                # 从 "Day 1" 格式中提取数字
                try:
                    day_number = int(date_str.split()[-1])
                except (ValueError, IndexError):
                    day_number = 1
            elif date_str.startswith("2025-"):
                # 从日期格式中提取天数（假设是连续的）
                try:
                    # 这里可以根据实际需求调整逻辑
                    day_number = 1  # 默认值，可以根据日期计算
                except:
                    day_number = 1

        return {
            "day": day_number,
            "theme": day_itinerary.get("theme", ""),
            "items": formatted_items,
            "total_distance": day_itinerary.get("total_distance", 0),
            "estimated_duration": day_itinerary.get("estimated_duration", ""),
            "notes": day_itinerary.get("notes", [])
        }

    def _convert_activity_to_item(self, activity: Dict[str, Any]) -> Dict[str, Any]:
        """将活动数据转换为标准的行程项目格式"""
        if not activity:
            return None

        # 提取位置信息
        location = activity.get("location") or activity.get("name") or activity.get("place")

        # 提取活动描述
        description = ""
        if activity.get("activity"):
            description = activity.get("activity")
        elif activity.get("activities"):
            # 处理activities数组
            activities_list = activity.get("activities")
            if isinstance(activities_list, list):
                description = "、".join(activities_list)
            else:
                description = str(activities_list)
        elif activity.get("description"):
            description = activity.get("description")
        elif activity.get("details"):
            description = activity.get("details")
        elif activity.get("content"):
            description = activity.get("content")

        # 提取时间信息
        start_time = ""
        end_time = ""

        if activity.get("time"):
            time_str = activity.get("time")
            if "-" in time_str:
                parts = time_str.split("-")
                start_time = parts[0].strip()
                end_time = parts[1].strip() if len(parts) > 1 else ""
            else:
                start_time = time_str.strip()
        elif activity.get("start"):
            start_time = activity.get("start")
            end_time = activity.get("end", "")

        # 构建POI数据
        poi_data = {
            "name": location or "未知地点",
            "address": activity.get("address", ""),
            "description": description,
            "rating": activity.get("rating", 0),
            "category": activity.get("category", "景点")
        }

        return {
            "type": "poi",  # 默认类型
            "poi": poi_data,
            "start_time": start_time,
            "end_time": end_time,
            "duration": activity.get("duration", ""),
            "description": description,
            "transport": activity.get("transport") or activity.get("transportation", {}),
            "notes": activity.get("notes", [])
        }

    def _format_itinerary_item(self, item: Dict[str, Any]) -> Dict[str, Any]:
        """格式化行程项目"""
        # 安全地处理POI数据
        poi_data = None
        if item.get("poi") and isinstance(item.get("poi"), dict):
            try:
                formatted_pois = self._format_poi_list([item["poi"]])
                if formatted_pois:
                    poi_data = formatted_pois[0]
            except Exception as e:
                logger.warning(f"格式化POI数据失败: {e}")
                poi_data = None

        return {
            "type": item.get("type", "poi"),  # poi, transport, meal, rest
            "poi": poi_data,
            "start_time": item.get("start_time", ""),
            "end_time": item.get("end_time", ""),
            "duration": item.get("duration", ""),
            "description": item.get("description", ""),
            "transport": item.get("transport", {}),
            "notes": item.get("notes", [])
        }

    def _extract_itinerary_highlights(self, daily_itineraries: List[Dict[str, Any]]) -> List[str]:
        """提取行程亮点"""
        highlights = []

        for day_itinerary in daily_itineraries:
            for item in day_itinerary.get("items", []):
                poi = item.get("poi", {})
                if poi.get("rating", 0) >= 4.5:
                    highlights.append(f"{poi.get('name', '')} - 高评分推荐")

                if "特色" in poi.get("tags", []) or "必游" in poi.get("tags", []):
                    highlights.append(f"{poi.get('name', '')} - 特色体验")

        return highlights[:5]  # 返回前5个亮点

    def _handle_optimization_events(
        self,
        state: Dict[str, Any],
        session_id: str
    ) -> List[str]:
        """处理优化事件 - 完成最终规划"""
        events = []

        # 发送最终优化进度
        events.append(self._format_sse_event(
            event_type="stage_progress",
            data={
                "session_id": session_id,
                "stage": "final_optimization",
                "progress": 99,
                "message": "正在进行最终优化..."
            }
        ))

        # 如果规划完成
        if state.get("is_completed"):
            # 生成完整的规划结果
            planning_result = self._generate_complete_planning_result(state)

            events.append(self._format_sse_event(
                event_type="planning_completed",
                data={
                    "session_id": session_id,
                    "message": "🎉 旅行规划已完成！",
                    "result": planning_result,
                    "summary": self._generate_planning_summary(state),
                    "total_days": len(state.get("daily_itineraries", [])),
                    "progress": 100,
                    "completion_time": self._get_current_timestamp()
                }
            ))

            # 发送语音播报文本
            events.append(self._format_sse_event(
                event_type="tts_content",
                data={
                    "session_id": session_id,
                    "content": self._generate_completion_tts_content(state),
                    "priority": "high"
                }
            ))

        return events

    def _generate_complete_planning_result(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """生成完整的规划结果"""
        return {
            "session_id": state.get("session_id", ""),
            "destinations": state.get("core_intent", {}).get("destinations", []),
            "travel_dates": state.get("core_intent", {}).get("travel_dates", {}),
            "daily_itineraries": [
                self._format_daily_itinerary(day)
                for day in state.get("daily_itineraries", [])
            ],
            "weather_info": state.get("weather_info", {}),
            "route_info": state.get("route_planning", {}),
            "charging_plan": state.get("charging_plan", []),
            "budget_estimate": state.get("budget_estimate", {}),
            "travel_tips": state.get("travel_tips", []),
            "emergency_contacts": state.get("emergency_contacts", [])
        }

    def _generate_planning_summary(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """生成规划摘要"""
        daily_itineraries = state.get("daily_itineraries", [])
        total_pois = sum(len(day.get("items", [])) for day in daily_itineraries)

        return {
            "total_days": len(daily_itineraries),
            "total_pois": total_pois,
            "destinations": state.get("core_intent", {}).get("destinations", []),
            "highlights": self._extract_itinerary_highlights(daily_itineraries),
            "estimated_budget": state.get("budget_estimate", {}).get("total", "待确认"),
            "travel_distance": state.get("route_planning", {}).get("total_distance", 0)
        }

    def _generate_completion_tts_content(self, state: Dict[str, Any]) -> str:
        """生成完成时的语音播报内容"""
        destinations = state.get("core_intent", {}).get("destinations", [])
        days = len(state.get("daily_itineraries", []))

        if len(destinations) == 1:
            return f"您的{destinations[0]}{days}日游规划已经完成！我为您精心安排了每天的行程，包括必游景点、特色美食和舒适住宿。祝您旅途愉快！"
        else:
            dest_str = "、".join(destinations)
            return f"您的{dest_str}{days}日游规划已经完成！这是一次精彩的多城市之旅，我已经为您优化了路线和时间安排。期待您的美好旅程！"

    def _get_current_timestamp(self) -> str:
        """获取当前时间戳"""
        from datetime import datetime
        return datetime.now().isoformat()
    
    def _handle_error_events(
        self, 
        state: Dict[str, Any], 
        session_id: str
    ) -> List[str]:
        """处理错误事件"""
        events = []
        
        events.append(self._format_sse_event(
            event_type="planning_failed",
            data={
                "session_id": session_id,
                "error": state.get("error_message", "未知错误"),
                "stage": state.get("current_stage", "unknown")
            }
        ))
        
        return events
    
    def _format_sse_event(
        self,
        event_type: str,
        data: Dict[str, Any],
        event_id: Optional[int] = None
    ) -> str:
        """
        格式化SSE事件

        Args:
            event_type: 事件类型
            data: 事件数据
            event_id: 事件ID（可选）

        Returns:
            格式化的SSE事件字符串
        """
        if event_id is None:
            self.event_id_counter += 1
            event_id = self.event_id_counter

        # 构建符合前端期望的事件格式
        event_data = {
            "event_type": event_type,
            "payload": data
        }

        # 确保数据可序列化
        try:
            data_json = json.dumps(event_data, ensure_ascii=False, default=str)
        except Exception as e:
            logger.warning(f"数据序列化失败: {str(e)}")
            data_json = json.dumps({
                "event_type": "error",
                "payload": {"error": "数据序列化失败"}
            }, ensure_ascii=False)

        # 构建SSE事件
        sse_event = f"id: {event_id}\n"
        sse_event += f"data: {data_json}\n\n"

        return sse_event
    
    def convert_final_state_to_response(self, state: Union[TravelPlanState, Dict[str, Any]]) -> Dict[str, Any]:
        """
        将最终状态转换为API响应格式

        Args:
            state: 最终状态（可以是TravelPlanState对象或字典）

        Returns:
            API响应格式的数据
        """
        try:
            # 确保state是字典格式，兼容TravelPlanState对象和字典
            if hasattr(state, '__dict__'):
                # 如果是对象，转换为字典
                state_dict = state.__dict__ if hasattr(state, '__dict__') else state
            else:
                # 如果已经是字典，直接使用
                state_dict = state

            response = {
                "session_id": state_dict.get("session_id", "unknown"),
                "user_id": state_dict.get("user_id", "unknown"),
                "status": "completed" if state_dict.get("is_completed") else "failed",
                "has_error": state_dict.get("has_error", False),
                "error_message": state_dict.get("error_message"),
                "planning_mode": state_dict.get("planning_mode"),
                "processing_time": state_dict.get("processing_time_seconds", 0),
                "created_at": state_dict.get("created_at"),
                "updated_at": state_dict.get("updated_at")
            }
            
            # 添加分析结果
            if "core_intent" in state_dict:
                response["core_intent"] = state_dict["core_intent"]

            if "multi_city_strategy" in state_dict:
                response["multi_city_strategy"] = state_dict["multi_city_strategy"]

            if "driving_context" in state_dict:
                response["driving_context"] = state_dict["driving_context"]

            if "preference_profile" in state_dict:
                response["preference_profile"] = state_dict["preference_profile"]

            # 添加行程结果
            if "daily_itineraries" in state_dict:
                response["daily_itineraries"] = state_dict["daily_itineraries"]

            if "summary" in state_dict:
                response["summary"] = state_dict["summary"]

            if "recommendations" in state_dict:
                response["recommendations"] = state_dict["recommendations"]

            # 添加元数据
            response["metadata"] = {
                "tokens_used": state_dict.get("tokens_used", 0),
                "cost_estimate": state_dict.get("cost_estimate", 0),
                "tool_calls_count": len(state_dict.get("tool_calls", [])),
                "api_calls_count": len(state_dict.get("api_calls", []))
            }
            
            return response
            
        except Exception as e:
            logger.error(f"状态转换失败: {str(e)}")
            # 安全地获取session_id
            session_id = "unknown"
            if hasattr(state, '__dict__'):
                session_id = getattr(state, 'session_id', 'unknown')
            elif isinstance(state, dict):
                session_id = state.get("session_id", "unknown")

            return {
                "session_id": session_id,
                "status": "error",
                "has_error": True,
                "error_message": f"状态转换失败: {str(e)}"
            }

    def _handle_analysis_complete_events(self, state: Dict[str, Any], session_id: str) -> List[str]:
        """
        处理分析完成事件

        Args:
            state: 包含完整分析结果的状态
            session_id: 会话ID

        Returns:
            List[str]: SSE事件列表
        """
        events = []

        try:
            # 发送分析完成事件，包含完整的分析结果
            events.append(self._format_sse_event(
                event_type="complete",
                data={
                    "session_id": session_id,
                    "stage": "analysis",
                    "message": "分析阶段完成",
                    "analysis_result": {
                        "session_id": state.get("session_id"),
                        "user_id": state.get("user_id"),
                        "query": state.get("query"),
                        "core_intent": state.get("core_intent"),
                        "user_profile": state.get("user_profile"),
                        "user_memories": state.get("user_memories"),
                        "travel_preferences": state.get("travel_preferences"),
                        "preference_profile": state.get("preference_profile"),
                        "driving_context": state.get("driving_context"),
                        "vehicle_info": state.get("vehicle_info"),
                        "multi_city_strategy": state.get("multi_city_strategy")
                    }
                }
            ))

            logger.info(f"发送分析完成事件 - Session: {session_id}")

        except Exception as e:
            logger.error(f"处理分析完成事件失败 - Session: {session_id}, 错误: {str(e)}")
            events.append(self._format_sse_event(
                event_type="error",
                data={
                    "session_id": session_id,
                    "error_message": f"分析完成事件处理失败: {str(e)}",
                    "stage": "analysis_complete"
                }
            ))

        return events
