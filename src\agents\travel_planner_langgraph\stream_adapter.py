"""
SSE流适配器 (Stream Adapter)

将LangGraph State快照转换为SSE事件流，确保与前端接口100%兼容。
"""

import json
import logging
from typing import Dict, Any, List, Optional, AsyncGenerator, Union
from datetime import datetime

from .state import TravelPlanState, ProcessingStage

logger = logging.getLogger(__name__)


class SSEStreamAdapter:
    """SSE流适配器类"""
    
    def __init__(self):
        """初始化SSE流适配器"""
        self.event_id_counter = 0
    
    async def convert_langgraph_stream_to_sse(
        self,
        langgraph_stream: AsyncGenerator[Dict[str, Any], None],
        session_id: str
    ) -> AsyncGenerator[str, None]:
        """
        将LangGraph流转换为SSE事件流
        
        Args:
            langgraph_stream: LangGraph状态流
            session_id: 会话ID
            
        Yields:
            SSE格式的事件字符串
        """
        try:
            logger.info(f"开始转换LangGraph流为SSE - Session: {session_id}")
            
            # 发送开始事件
            yield self._format_sse_event(
                event_type="stream_start",
                data={
                    "session_id": session_id,
                    "timestamp": datetime.now().isoformat(),
                    "message": "开始处理您的旅行规划请求..."
                }
            )
            
            async for chunk in langgraph_stream:
                # 处理每个状态块
                for node_name, state_update in chunk.items():
                    if isinstance(state_update, dict):
                        # 转换状态更新为SSE事件
                        sse_events = self._convert_state_to_sse_events(
                            node_name, 
                            state_update, 
                            session_id
                        )
                        
                        for event in sse_events:
                            yield event
            
            # 发送结束事件
            yield self._format_sse_event(
                event_type="stream_end",
                data={
                    "session_id": session_id,
                    "timestamp": datetime.now().isoformat(),
                    "message": "处理完成"
                }
            )
            
            logger.info(f"LangGraph流转换完成 - Session: {session_id}")
            
        except Exception as e:
            logger.error(f"LangGraph流转换失败 - Session: {session_id}, 错误: {str(e)}")
            
            # 发送错误事件
            yield self._format_sse_event(
                event_type="error",
                data={
                    "session_id": session_id,
                    "error": str(e),
                    "timestamp": datetime.now().isoformat()
                }
            )
    
    def _convert_state_to_sse_events(
        self,
        node_name: str,
        state: Dict[str, Any],
        session_id: str
    ) -> List[str]:
        """
        将状态更新转换为SSE事件列表
        
        Args:
            node_name: 节点名称
            state: 状态数据
            session_id: 会话ID
            
        Returns:
            SSE事件字符串列表
        """
        events = []
        
        try:
            # 获取当前阶段
            current_stage = state.get("current_stage", "unknown")
            
            # 根据节点类型生成不同的事件
            if node_name == "core_intent_analyzer":
                events.extend(self._handle_intent_analysis_events(state, session_id))
            elif node_name == "multi_city_strategy":
                events.extend(self._handle_multi_city_events(state, session_id))
            elif node_name == "driving_context_analyzer":
                events.extend(self._handle_driving_context_events(state, session_id))
            elif node_name == "preference_analyzer":
                events.extend(self._handle_preference_events(state, session_id))
            elif node_name == "itinerary_generator":
                events.extend(self._handle_itinerary_events(state, session_id))
            elif node_name == "optimizer":
                events.extend(self._handle_optimization_events(state, session_id))
            elif node_name == "error_handler":
                events.extend(self._handle_error_events(state, session_id))
            
            # 处理状态中的事件列表
            if "events" in state:
                for event in state["events"]:
                    events.append(self._format_sse_event(
                        event_type=event.get("type", "update"),
                        data={
                            "session_id": session_id,
                            "node": node_name,
                            "stage": current_stage,
                            **event.get("data", {})
                        },
                        event_id=event.get("id")
                    ))
            
        except Exception as e:
            logger.error(f"状态转换失败 - 节点: {node_name}, 错误: {str(e)}")
            events.append(self._format_sse_event(
                event_type="conversion_error",
                data={
                    "session_id": session_id,
                    "node": node_name,
                    "error": str(e)
                }
            ))
        
        return events
    
    def _handle_intent_analysis_events(
        self,
        state: Dict[str, Any],
        session_id: str
    ) -> List[str]:
        """处理意图分析事件"""
        events = []

        # 阶段开始事件
        events.append(self._format_sse_event(
            event_type="stage_progress",
            data={
                "session_id": session_id,
                "stage": "intent_analysis",
                "progress": 10,
                "message": "正在分析您的旅行意图..."
            }
        ))

        # 如果有核心意图结果，发送analysis_step事件
        if "core_intent" in state:
            core_intent = state["core_intent"]

            # 构建用户需求分析内容
            destinations_str = "、".join(core_intent.get("destinations", []))
            days = core_intent.get("days", 0)
            travel_theme = core_intent.get("travel_theme", "")
            travelers = core_intent.get("travelers", {})
            budget = core_intent.get("budget", {})
            transportation = core_intent.get("transportation", {})

            # 构建分析内容文本
            content_parts = []
            if destinations_str and days:
                content_parts.append(f"{destinations_str}|{days}天")
            if travelers.get("composition"):
                content_parts.append(travelers["composition"])
            if travel_theme:
                content_parts.append(travel_theme)
            if budget.get("level"):
                content_parts.append(f"预算：{budget['level']}")
            if transportation.get("primary_mode"):
                mode_map = {"self_driving": "自驾", "public_transport": "公共交通", "flight": "飞机"}
                mode_text = mode_map.get(transportation["primary_mode"], transportation["primary_mode"])
                if transportation.get("distance_km"):
                    content_parts.append(f"续航里程：{transportation['distance_km']}KM")
                else:
                    content_parts.append(mode_text)

            analysis_content = " | ".join(content_parts) if content_parts else "已解析用户基本需求"

            events.append(self._format_sse_event(
                event_type="analysis_step",
                data={
                    "session_id": session_id,
                    "step_type": "user_intent",
                    "title": "解析用户需求和画像",
                    "content": analysis_content,
                    "confidence": core_intent.get("confidence_score", 0),
                    "completed": True
                }
            ))

        return events
    
    def _handle_multi_city_events(
        self, 
        state: Dict[str, Any], 
        session_id: str
    ) -> List[str]:
        """处理多城市策略事件"""
        events = []
        
        events.append(self._format_sse_event(
            event_type="stage_progress",
            data={
                "session_id": session_id,
                "stage": "multi_city_strategy",
                "progress": 30,
                "message": "正在制定多城市旅行策略..."
            }
        ))
        
        if "multi_city_strategy" in state:
            strategy = state["multi_city_strategy"]
            events.append(self._format_sse_event(
                event_type="strategy_planned",
                data={
                    "session_id": session_id,
                    "strategy_type": strategy.get("strategy_type"),
                    "recommended_order": strategy.get("recommended_order", []),
                    "progress": 40
                }
            ))
        
        return events
    
    def _handle_driving_context_events(
        self, 
        state: Dict[str, Any], 
        session_id: str
    ) -> List[str]:
        """处理驾驶情境事件"""
        events = []
        
        events.append(self._format_sse_event(
            event_type="stage_progress",
            data={
                "session_id": session_id,
                "stage": "driving_context",
                "progress": 45,
                "message": "正在分析驾驶需求..."
            }
        ))
        
        if "driving_context" in state:
            context = state["driving_context"]
            events.append(self._format_sse_event(
                event_type="driving_analyzed",
                data={
                    "session_id": session_id,
                    "driving_strategy": context.get("driving_strategy"),
                    "planning_mode": state.get("planning_mode"),
                    "progress": 50
                }
            ))
        
        return events
    
    def _handle_preference_events(
        self,
        state: Dict[str, Any],
        session_id: str
    ) -> List[str]:
        """处理偏好分析事件"""
        events = []

        events.append(self._format_sse_event(
            event_type="stage_progress",
            data={
                "session_id": session_id,
                "stage": "preference_analysis",
                "progress": 60,
                "message": "正在分析您的旅行偏好..."
            }
        ))

        if "preference_profile" in state:
            profile = state["preference_profile"]

            # 发送景点偏好分析结果
            attraction_prefs = profile.get("attraction_preferences", {})
            if attraction_prefs:
                # 提取景点偏好信息
                attraction_content_parts = []
                if attraction_prefs.get("preferred_types"):
                    types_str = "、".join(attraction_prefs["preferred_types"])
                    attraction_content_parts.append(f"景点推荐：{types_str}")
                else:
                    attraction_content_parts.append("景点推荐：乐园/休闲")

                if attraction_prefs.get("depth_preference"):
                    attraction_content_parts.append(attraction_prefs["depth_preference"])
                else:
                    attraction_content_parts.append("是否深度游、时间节奏")

                attraction_content = " | ".join(attraction_content_parts) if attraction_content_parts else "景点推荐：乐园/休闲/"

                events.append(self._format_sse_event(
                    event_type="analysis_step",
                    data={
                        "session_id": session_id,
                        "step_type": "poi_preference",
                        "title": "景点偏好类型",
                        "content": attraction_content,
                        "confidence": attraction_prefs.get("confidence_score", 0),
                        "completed": True
                    }
                ))

            # 发送美食偏好分析结果
            food_prefs = profile.get("food_preferences", {})
            if food_prefs:
                # 提取美食偏好信息
                food_content_parts = []
                if food_prefs.get("taste_preferences"):
                    tastes = "、".join(food_prefs["taste_preferences"])
                    food_content_parts.append(tastes)
                if food_prefs.get("restaurant_types"):
                    types = "、".join(food_prefs["restaurant_types"])
                    food_content_parts.append(types)
                if food_prefs.get("environment_preferences"):
                    env = "、".join(food_prefs["environment_preferences"])
                    food_content_parts.append(env)
                if food_prefs.get("price_sensitivity"):
                    food_content_parts.append(food_prefs["price_sensitivity"])
                if food_prefs.get("special_requirements"):
                    reqs = "、".join(food_prefs["special_requirements"])
                    food_content_parts.append(reqs)

                # 如果没有具体偏好信息，使用默认内容
                if not food_content_parts:
                    food_content = "不辣、口碑老店、干净、性价比高"
                else:
                    food_content = "、".join(food_content_parts)

                events.append(self._format_sse_event(
                    event_type="analysis_step",
                    data={
                        "session_id": session_id,
                        "step_type": "food_preference",
                        "title": "美食偏好",
                        "content": food_content,
                        "confidence": food_prefs.get("confidence_score", 0),
                        "completed": True
                    }
                ))

            # 添加住宿偏好分析（基于用户记忆或默认分析）
            core_intent = state.get("core_intent", {})
            budget = core_intent.get("budget", {})
            travelers = core_intent.get("travelers", {})

            # 构建住宿偏好内容
            accommodation_content_parts = []
            if budget.get("accommodation_range"):
                accommodation_content_parts.append(f"连锁{budget['accommodation_range']}")
            else:
                accommodation_content_parts.append("连锁1800-1500元")

            if travelers.get("has_children"):
                accommodation_content_parts.append("免费洗衣房")

            accommodation_content_parts.extend(["安静整洁", "免费停车"])

            accommodation_content = "、".join(accommodation_content_parts)

            events.append(self._format_sse_event(
                event_type="analysis_step",
                data={
                    "session_id": session_id,
                    "step_type": "accommodation_preference",
                    "title": "住宿偏好",
                    "content": accommodation_content,
                    "confidence": 0.8,
                    "completed": True
                }
            ))

        return events
    
    def _handle_itinerary_events(
        self, 
        state: Dict[str, Any], 
        session_id: str
    ) -> List[str]:
        """处理行程生成事件"""
        events = []
        
        events.append(self._format_sse_event(
            event_type="stage_progress",
            data={
                "session_id": session_id,
                "stage": "itinerary_generation",
                "progress": 80,
                "message": "正在生成详细行程..."
            }
        ))
        
        if "daily_itineraries" in state:
            itineraries = state["daily_itineraries"]
            events.append(self._format_sse_event(
                event_type="itinerary_generated",
                data={
                    "session_id": session_id,
                    "daily_count": len(itineraries),
                    "summary": state.get("summary", {}),
                    "progress": 90
                }
            ))
        
        return events
    
    def _handle_optimization_events(
        self, 
        state: Dict[str, Any], 
        session_id: str
    ) -> List[str]:
        """处理优化事件"""
        events = []
        
        if state.get("is_completed"):
            events.append(self._format_sse_event(
                event_type="planning_completed",
                data={
                    "session_id": session_id,
                    "message": "旅行规划已完成！",
                    "summary": state.get("summary", {}),
                    "total_days": len(state.get("daily_itineraries", [])),
                    "progress": 100
                }
            ))
        
        return events
    
    def _handle_error_events(
        self, 
        state: Dict[str, Any], 
        session_id: str
    ) -> List[str]:
        """处理错误事件"""
        events = []
        
        events.append(self._format_sse_event(
            event_type="planning_failed",
            data={
                "session_id": session_id,
                "error": state.get("error_message", "未知错误"),
                "stage": state.get("current_stage", "unknown")
            }
        ))
        
        return events
    
    def _format_sse_event(
        self,
        event_type: str,
        data: Dict[str, Any],
        event_id: Optional[int] = None
    ) -> str:
        """
        格式化SSE事件

        Args:
            event_type: 事件类型
            data: 事件数据
            event_id: 事件ID（可选）

        Returns:
            格式化的SSE事件字符串
        """
        if event_id is None:
            self.event_id_counter += 1
            event_id = self.event_id_counter

        # 构建符合前端期望的事件格式
        event_data = {
            "event_type": event_type,
            "payload": data
        }

        # 确保数据可序列化
        try:
            data_json = json.dumps(event_data, ensure_ascii=False, default=str)
        except Exception as e:
            logger.warning(f"数据序列化失败: {str(e)}")
            data_json = json.dumps({
                "event_type": "error",
                "payload": {"error": "数据序列化失败"}
            }, ensure_ascii=False)

        # 构建SSE事件
        sse_event = f"id: {event_id}\n"
        sse_event += f"data: {data_json}\n\n"

        return sse_event
    
    def convert_final_state_to_response(self, state: Union[TravelPlanState, Dict[str, Any]]) -> Dict[str, Any]:
        """
        将最终状态转换为API响应格式

        Args:
            state: 最终状态（可以是TravelPlanState对象或字典）

        Returns:
            API响应格式的数据
        """
        try:
            # 确保state是字典格式，兼容TravelPlanState对象和字典
            if hasattr(state, '__dict__'):
                # 如果是对象，转换为字典
                state_dict = state.__dict__ if hasattr(state, '__dict__') else state
            else:
                # 如果已经是字典，直接使用
                state_dict = state

            response = {
                "session_id": state_dict.get("session_id", "unknown"),
                "user_id": state_dict.get("user_id", "unknown"),
                "status": "completed" if state_dict.get("is_completed") else "failed",
                "has_error": state_dict.get("has_error", False),
                "error_message": state_dict.get("error_message"),
                "planning_mode": state_dict.get("planning_mode"),
                "processing_time": state_dict.get("processing_time_seconds", 0),
                "created_at": state_dict.get("created_at"),
                "updated_at": state_dict.get("updated_at")
            }
            
            # 添加分析结果
            if "core_intent" in state_dict:
                response["core_intent"] = state_dict["core_intent"]

            if "multi_city_strategy" in state_dict:
                response["multi_city_strategy"] = state_dict["multi_city_strategy"]

            if "driving_context" in state_dict:
                response["driving_context"] = state_dict["driving_context"]

            if "preference_profile" in state_dict:
                response["preference_profile"] = state_dict["preference_profile"]

            # 添加行程结果
            if "daily_itineraries" in state_dict:
                response["daily_itineraries"] = state_dict["daily_itineraries"]

            if "summary" in state_dict:
                response["summary"] = state_dict["summary"]

            if "recommendations" in state_dict:
                response["recommendations"] = state_dict["recommendations"]

            # 添加元数据
            response["metadata"] = {
                "tokens_used": state_dict.get("tokens_used", 0),
                "cost_estimate": state_dict.get("cost_estimate", 0),
                "tool_calls_count": len(state_dict.get("tool_calls", [])),
                "api_calls_count": len(state_dict.get("api_calls", []))
            }
            
            return response
            
        except Exception as e:
            logger.error(f"状态转换失败: {str(e)}")
            # 安全地获取session_id
            session_id = "unknown"
            if hasattr(state, '__dict__'):
                session_id = getattr(state, 'session_id', 'unknown')
            elif isinstance(state, dict):
                session_id = state.get("session_id", "unknown")

            return {
                "session_id": session_id,
                "status": "error",
                "has_error": True,
                "error_message": f"状态转换失败: {str(e)}"
            }
