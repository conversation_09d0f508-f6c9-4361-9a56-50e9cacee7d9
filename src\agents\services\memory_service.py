"""
记忆服务 (Memory Service)

提供用户记忆管理的原子化服务，包括记忆存储、检索、更新等功能。
"""

import json
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta

from src.database.mongodb_client import get_mongo_client
from src.database.redis_client import RedisClient

logger = logging.getLogger(__name__)


class MemoryService:
    """记忆服务类"""
    
    def __init__(self):
        """初始化记忆服务"""
        self.mongodb_client = None  # 将在使用时获取
        self.redis_client = RedisClient()
        self.memory_collection = "user_memories"
        self.cache_ttl = 3600  # 缓存1小时

    async def _get_mongodb_client(self):
        """获取MongoDB客户端实例"""
        if self.mongodb_client is None:
            self.mongodb_client = await get_mongo_client()
        return self.mongodb_client
    
    async def get_user_memories(
        self,
        user_id: str,
        limit: int = 50,
        memory_type: Optional[str] = None,
        days_back: Optional[int] = None
    ) -> List[Dict[str, Any]]:
        """
        获取用户记忆
        
        Args:
            user_id: 用户ID
            limit: 返回数量限制
            memory_type: 记忆类型筛选
            days_back: 获取多少天内的记忆
            
        Returns:
            用户记忆列表
        """
        try:
            logger.info(f"获取用户记忆 - 用户ID: {user_id}, 限制: {limit}")
            
            # 先尝试从缓存获取
            cache_key = f"user_memories:{user_id}:{memory_type}:{days_back}:{limit}"
            cached_memories = await self.redis_client.get(cache_key)
            
            if cached_memories:
                logger.info(f"从缓存获取用户记忆 - 用户ID: {user_id}")
                return json.loads(cached_memories)
            
            # 构建查询条件
            query = {"user_id": user_id}
            
            if memory_type:
                query["memory_type"] = memory_type
            
            if days_back:
                cutoff_date = datetime.now() - timedelta(days=days_back)
                query["created_at"] = {"$gte": cutoff_date}
            
            # 从MongoDB查询
            mongodb_client = await self._get_mongodb_client()
            memories = await mongodb_client.get_user_memories(
                user_id=user_id,
                memory_type=memory_type,
                limit=limit
            )
            
            # 转换日期格式（ObjectId已经在MongoDB客户端中转换）
            result = []
            for memory in memories:
                if "created_at" in memory and hasattr(memory["created_at"], 'isoformat'):
                    memory["created_at"] = memory["created_at"].isoformat()
                if "updated_at" in memory and hasattr(memory["updated_at"], 'isoformat'):
                    memory["updated_at"] = memory["updated_at"].isoformat()
                result.append(memory)
            
            # 缓存结果
            await self.redis_client.set(
                cache_key,
                json.dumps(result, ensure_ascii=False),
                ttl=self.cache_ttl
            )
            
            logger.info(f"获取用户记忆成功 - 用户ID: {user_id}, 数量: {len(result)}")
            return result
            
        except Exception as e:
            logger.error(f"获取用户记忆失败 - 用户ID: {user_id}, 错误: {str(e)}")
            return []
    
    async def save_memory(
        self,
        user_id: str,
        memory_type: str,
        content: Dict[str, Any],
        metadata: Optional[Dict[str, Any]] = None,
        importance: int = 5,
        tags: Optional[List[str]] = None
    ) -> str:
        """
        保存用户记忆
        
        Args:
            user_id: 用户ID
            memory_type: 记忆类型（travel_query, preference, experience等）
            content: 记忆内容
            metadata: 元数据
            importance: 重要性评分（1-10）
            tags: 标签列表
            
        Returns:
            记忆ID
        """
        try:
            logger.info(f"保存用户记忆 - 用户ID: {user_id}, 类型: {memory_type}")
            
            memory_doc = {
                "user_id": user_id,
                "memory_type": memory_type,
                "content": content,
                "metadata": metadata or {},
                "importance": importance,
                "tags": tags or [],
                "created_at": datetime.now(),
                "updated_at": datetime.now(),
                "access_count": 0,
                "last_accessed": None
            }
            
            # 插入到MongoDB
            mongodb_client = await self._get_mongodb_client()
            memory_id = await mongodb_client.create_memory(memory_doc)
            
            # 清除相关缓存
            await self._clear_user_memory_cache(user_id)
            
            logger.info(f"用户记忆保存成功 - 记忆ID: {memory_id}")
            return memory_id
            
        except Exception as e:
            logger.error(f"保存用户记忆失败 - 用户ID: {user_id}, 错误: {str(e)}")
            raise
    
    async def update_memory(
        self,
        memory_id: str,
        update_data: Dict[str, Any]
    ) -> bool:
        """
        更新用户记忆
        
        Args:
            memory_id: 记忆ID
            update_data: 更新数据
            
        Returns:
            是否更新成功
        """
        try:
            logger.info(f"更新用户记忆 - 记忆ID: {memory_id}")
            
            from bson import ObjectId
            
            update_data["updated_at"] = datetime.now()
            
            mongodb_client = await self._get_mongodb_client()
            result = await mongodb_client.update_one(
                self.memory_collection,
                {"_id": ObjectId(memory_id)},
                {"$set": update_data}
            )

            if result.modified_count > 0:
                # 获取用户ID并清除缓存
                memory = await mongodb_client.find_one(
                    self.memory_collection,
                    {"_id": ObjectId(memory_id)}
                )
                if memory:
                    await self._clear_user_memory_cache(memory["user_id"])
                
                logger.info(f"用户记忆更新成功 - 记忆ID: {memory_id}")
                return True
            else:
                logger.warning(f"用户记忆更新失败 - 记忆ID: {memory_id}")
                return False
                
        except Exception as e:
            logger.error(f"更新用户记忆失败 - 记忆ID: {memory_id}, 错误: {str(e)}")
            return False
    
    async def search_memories(
        self,
        user_id: str,
        query: str,
        memory_types: Optional[List[str]] = None,
        limit: int = 20
    ) -> List[Dict[str, Any]]:
        """
        搜索用户记忆
        
        Args:
            user_id: 用户ID
            query: 搜索查询
            memory_types: 记忆类型列表
            limit: 返回数量限制
            
        Returns:
            搜索结果列表
        """
        try:
            logger.info(f"搜索用户记忆 - 用户ID: {user_id}, 查询: {query}")
            
            # 构建搜索条件
            search_filter = {
                "user_id": user_id,
                "$text": {"$search": query}
            }
            
            if memory_types:
                search_filter["memory_type"] = {"$in": memory_types}
            
            # 执行搜索
            mongodb_client = await self._get_mongodb_client()
            memories = await mongodb_client.find(
                self.memory_collection,
                search_filter,
                sort=[("score", {"$meta": "textScore"})],
                limit=limit
            )
            
            # 转换结果
            result = []
            for memory in memories:
                memory["_id"] = str(memory["_id"])
                if "created_at" in memory:
                    memory["created_at"] = memory["created_at"].isoformat()
                if "updated_at" in memory:
                    memory["updated_at"] = memory["updated_at"].isoformat()
                result.append(memory)
            
            logger.info(f"搜索用户记忆完成 - 找到 {len(result)} 条记录")
            return result
            
        except Exception as e:
            logger.error(f"搜索用户记忆失败 - 用户ID: {user_id}, 错误: {str(e)}")
            return []
    
    async def get_memory_by_id(self, memory_id: str) -> Optional[Dict[str, Any]]:
        """
        根据ID获取记忆
        
        Args:
            memory_id: 记忆ID
            
        Returns:
            记忆信息
        """
        try:
            from bson import ObjectId
            
            mongodb_client = await self._get_mongodb_client()
            memory = await mongodb_client.find_one(
                self.memory_collection,
                {"_id": ObjectId(memory_id)}
            )

            if memory:
                # 更新访问统计
                await mongodb_client.update_one(
                    self.memory_collection,
                    {"_id": ObjectId(memory_id)},
                    {
                        "$inc": {"access_count": 1},
                        "$set": {"last_accessed": datetime.now()}
                    }
                )
                
                # 转换格式
                memory["_id"] = str(memory["_id"])
                if "created_at" in memory:
                    memory["created_at"] = memory["created_at"].isoformat()
                if "updated_at" in memory:
                    memory["updated_at"] = memory["updated_at"].isoformat()
                if "last_accessed" in memory and memory["last_accessed"]:
                    memory["last_accessed"] = memory["last_accessed"].isoformat()
                
                return memory
            
            return None
            
        except Exception as e:
            logger.error(f"获取记忆失败 - 记忆ID: {memory_id}, 错误: {str(e)}")
            return None
    
    async def delete_memory(self, memory_id: str) -> bool:
        """
        删除记忆
        
        Args:
            memory_id: 记忆ID
            
        Returns:
            是否删除成功
        """
        try:
            from bson import ObjectId
            
            # 获取用户ID用于清除缓存
            mongodb_client = await self._get_mongodb_client()
            memory = await mongodb_client.find_one(
                self.memory_collection,
                {"_id": ObjectId(memory_id)}
            )

            if not memory:
                return False

            result = await mongodb_client.delete_one(
                self.memory_collection,
                {"_id": ObjectId(memory_id)}
            )
            
            if result.deleted_count > 0:
                # 清除缓存
                await self._clear_user_memory_cache(memory["user_id"])
                logger.info(f"记忆删除成功 - 记忆ID: {memory_id}")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"删除记忆失败 - 记忆ID: {memory_id}, 错误: {str(e)}")
            return False
    
    async def get_memory_statistics(self, user_id: str) -> Dict[str, Any]:
        """
        获取用户记忆统计
        
        Args:
            user_id: 用户ID
            
        Returns:
            记忆统计信息
        """
        try:
            pipeline = [
                {"$match": {"user_id": user_id}},
                {
                    "$group": {
                        "_id": "$memory_type",
                        "count": {"$sum": 1},
                        "avg_importance": {"$avg": "$importance"},
                        "total_access": {"$sum": "$access_count"}
                    }
                }
            ]
            
            mongodb_client = await self._get_mongodb_client()
            stats = await mongodb_client.aggregate(self.memory_collection, pipeline)
            
            result = {
                "total_memories": 0,
                "by_type": {},
                "total_access": 0,
                "avg_importance": 0
            }
            
            total_importance = 0
            for stat in stats:
                memory_type = stat["_id"]
                count = stat["count"]
                avg_importance = stat["avg_importance"]
                total_access = stat["total_access"]
                
                result["by_type"][memory_type] = {
                    "count": count,
                    "avg_importance": avg_importance,
                    "total_access": total_access
                }
                
                result["total_memories"] += count
                result["total_access"] += total_access
                total_importance += avg_importance * count
            
            if result["total_memories"] > 0:
                result["avg_importance"] = total_importance / result["total_memories"]
            
            return result
            
        except Exception as e:
            logger.error(f"获取记忆统计失败 - 用户ID: {user_id}, 错误: {str(e)}")
            return {}
    
    async def _clear_user_memory_cache(self, user_id: str):
        """清除用户记忆相关缓存"""
        try:
            pattern = f"user_memories:{user_id}:*"
            keys = await self.redis_client.keys(pattern)
            if keys:
                await self.redis_client.delete(*keys)
                logger.info(f"清除用户记忆缓存 - 用户ID: {user_id}, 清除键数: {len(keys)}")
        except Exception as e:
            logger.warning(f"清除用户记忆缓存失败 - 用户ID: {user_id}, 错误: {str(e)}")
